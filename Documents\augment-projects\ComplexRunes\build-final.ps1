Write-Host "Building Complex Runes Mod - Final Version" -ForegroundColor Green
Write-Host ""

# Set JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Java\jdk-21"
Write-Host "Using JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Green
Write-Host ""

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "- Minecraft: 1.21.1" -ForegroundColor White
Write-Host "- Fabric Loader: 0.16.9" -ForegroundColor White
Write-Host "- Fabric API: 0.102.0+1.21.1" -ForegroundColor White
Write-Host "- Fabric Loom: 1.6-SNAPSHOT" -ForegroundColor White
Write-Host "- Gradle: 8.8" -ForegroundColor White
Write-Host ""

Write-Host "Starting build process..." -ForegroundColor Yellow
Write-Host ""

Write-Host "Step 1: Cleaning previous build..." -ForegroundColor Cyan
.\gradlew clean

if ($LASTEXITCODE -eq 0) {
    Write-Host "Clean successful" -ForegroundColor Green
    Write-Host ""

    Write-Host "Step 2: Building mod..." -ForegroundColor Cyan
    .\gradlew build

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "================================" -ForegroundColor Green
        Write-Host "BUILD SUCCESS!" -ForegroundColor Green
        Write-Host "================================" -ForegroundColor Green
        Write-Host ""

        # Check for the built jar file
        $jarFiles = Get-ChildItem "build\libs\*.jar" -ErrorAction SilentlyContinue

        if ($jarFiles) {
            foreach ($jar in $jarFiles) {
                $fileSize = [math]::Round($jar.Length/1KB, 2)
                Write-Host "Built: $($jar.Name)" -ForegroundColor Cyan
                Write-Host "Size: $fileSize KB" -ForegroundColor White
                Write-Host "Path: $($jar.FullName)" -ForegroundColor White
            }

            Write-Host ""
            Write-Host "Your Complex Runes mod is ready!" -ForegroundColor Green
            Write-Host ""
            Write-Host "Installation Instructions:" -ForegroundColor Yellow
            Write-Host "1. Install Fabric Loader 0.16.9+ for Minecraft 1.21.1" -ForegroundColor White
            Write-Host "2. Download Fabric API 0.102.0+1.21.1 from Modrinth/CurseForge" -ForegroundColor White
            Write-Host "3. Download Cloth Config 15.0.140+ from Modrinth/CurseForge" -ForegroundColor White
            Write-Host "4. Download ModMenu 11.0.2+ from Modrinth/CurseForge (optional)" -ForegroundColor White
            Write-Host "5. Copy complex-runes-1.0.0.jar to your .minecraft\mods\ folder" -ForegroundColor White
            Write-Host ""
            Write-Host "Features:" -ForegroundColor Yellow
            Write-Host "- Monitors chat for rune notifications" -ForegroundColor White
            Write-Host "- Displays countdown timers on screen" -ForegroundColor White
            Write-Host "- Configurable keywords and regex patterns" -ForegroundColor White
            Write-Host "- Draggable text positioning" -ForegroundColor White
            Write-Host "- ModMenu configuration GUI" -ForegroundColor White
            Write-Host ""
            Write-Host "To test in development:" -ForegroundColor Cyan
            Write-Host ".\gradlew runClient" -ForegroundColor White

        } else {
            Write-Host "Build succeeded but no JAR files found in build\libs\" -ForegroundColor Yellow
            Write-Host "Check the build output above for any issues." -ForegroundColor Yellow
        }

    } else {
        Write-Host ""
        Write-Host "================================" -ForegroundColor Red
        Write-Host "BUILD FAILED!" -ForegroundColor Red
        Write-Host "================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "Common solutions:" -ForegroundColor Yellow
        Write-Host "1. Check that Java 21 is properly installed" -ForegroundColor White
        Write-Host "2. Ensure internet connection for dependency downloads" -ForegroundColor White
        Write-Host "3. Try running: .\gradlew clean build --refresh-dependencies" -ForegroundColor White
        Write-Host "4. Check the error messages above for specific issues" -ForegroundColor White
    }

} else {
    Write-Host ""
    Write-Host "Clean failed!" -ForegroundColor Red
    Write-Host "Check the error messages above for details." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to continue"
