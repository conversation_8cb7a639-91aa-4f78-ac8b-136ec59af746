package com.complexrunes.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.complexrunes.ComplexRunesMod;
import net.fabricmc.loader.api.FabricLoader;
import org.slf4j.Logger;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class RuneModConfig {
    private static final Logger LOGGER = ComplexRunesMod.LOGGER;
    private static final File CONFIG_FILE = new File(FabricLoader.getInstance().getConfigDir().toFile(), "complex-runes.json");
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    
    private static RuneModConfig instance;

    // Chat monitoring settings
    private boolean enabled = true;
    private List<String> runeKeywords = Arrays.asList("rune", "Rune", "RUNE");
    private boolean useRegex = false;
    private String customRegexPattern = ".*[Rr]une.*";
    private boolean caseSensitive = false;

    // Display settings
    private int displayDurationSeconds = 30;
    private boolean showCountdown = true;
    private int titleX = -1; // -1 means centered
    private int titleY = 50;
    private float titleScale = 1.0f;
    private int titleColor = 0xFFFFFF; // White
    private boolean draggable = false;

    // Visual effects
    private boolean showTitle = true;
    private boolean useShadow = true;

    private RuneModConfig() {
        load();
    }

    public static RuneModConfig getInstance() {
        if (instance == null) {
            instance = new RuneModConfig();
        }
        return instance;
    }

    public void load() {
        if (CONFIG_FILE.exists()) {
            try (FileReader reader = new FileReader(CONFIG_FILE)) {
                RuneModConfig loaded = GSON.fromJson(reader, RuneModConfig.class);
                if (loaded != null) {
                    copyFrom(loaded);
                }
                LOGGER.info("Loaded configuration from {}", CONFIG_FILE.getPath());
            } catch (IOException e) {
                LOGGER.error("Failed to load config: {}", e.getMessage());
            }
        } else {
            save(); // Create default config file
        }
    }

    public void save() {
        try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
            GSON.toJson(this, writer);
            LOGGER.info("Saved configuration to {}", CONFIG_FILE.getPath());
        } catch (IOException e) {
            LOGGER.error("Failed to save config: {}", e.getMessage());
        }
    }

    private void copyFrom(RuneModConfig other) {
        this.enabled = other.enabled;
        this.runeKeywords = other.runeKeywords;
        this.useRegex = other.useRegex;
        this.customRegexPattern = other.customRegexPattern;
        this.caseSensitive = other.caseSensitive;
        this.displayDurationSeconds = other.displayDurationSeconds;
        this.showCountdown = other.showCountdown;
        this.titleX = other.titleX;
        this.titleY = other.titleY;
        this.titleScale = other.titleScale;
        this.titleColor = other.titleColor;
        this.draggable = other.draggable;
        this.showTitle = other.showTitle;
        this.useShadow = other.useShadow;
    }

    // Getters
    public boolean isEnabled() { return enabled; }
    public List<String> getRuneKeywords() { return runeKeywords; }
    public boolean isUseRegex() { return useRegex; }
    public String getCustomRegexPattern() { return customRegexPattern; }
    public boolean isCaseSensitive() { return caseSensitive; }
    public int getDisplayDurationSeconds() { return displayDurationSeconds; }
    public boolean isShowCountdown() { return showCountdown; }
    public int getTitleX() { return titleX; }
    public int getTitleY() { return titleY; }
    public float getTitleScale() { return titleScale; }
    public int getTitleColor() { return titleColor; }
    public boolean isDraggable() { return draggable; }
    public boolean isShowTitle() { return showTitle; }
    public boolean isUseShadow() { return useShadow; }

    // Setters
    public void setEnabled(boolean enabled) { this.enabled = enabled; save(); }
    public void setRuneKeywords(List<String> runeKeywords) { this.runeKeywords = runeKeywords; save(); }
    public void setUseRegex(boolean useRegex) { this.useRegex = useRegex; save(); }
    public void setCustomRegexPattern(String customRegexPattern) { this.customRegexPattern = customRegexPattern; save(); }
    public void setCaseSensitive(boolean caseSensitive) { this.caseSensitive = caseSensitive; save(); }
    public void setDisplayDurationSeconds(int displayDurationSeconds) { this.displayDurationSeconds = displayDurationSeconds; save(); }
    public void setShowCountdown(boolean showCountdown) { this.showCountdown = showCountdown; save(); }
    public void setTitleX(int titleX) { this.titleX = titleX; save(); }
    public void setTitleY(int titleY) { this.titleY = titleY; save(); }
    public void setTitleScale(float titleScale) { this.titleScale = titleScale; save(); }
    public void setTitleColor(int titleColor) { this.titleColor = titleColor; save(); }
    public void setDraggable(boolean draggable) { this.draggable = draggable; save(); }
    public void setShowTitle(boolean showTitle) { this.showTitle = showTitle; save(); }
    public void setUseShadow(boolean useShadow) { this.useShadow = useShadow; save(); }
}
