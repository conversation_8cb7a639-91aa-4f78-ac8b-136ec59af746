package com.complexrunes.title;

import com.complexrunes.config.RuneModConfig;
import com.complexrunes.render.RuneTextRenderer;
import com.complexrunes.ComplexRunesMod;
import net.minecraft.client.MinecraftClient;
import org.slf4j.Logger;

/**
 * Manages title displays for rune notifications
 */
public class TitleManager {
    private static final Logger LOGGER = ComplexRunesMod.LOGGER;

    /**
     * Shows a rune notification for a player
     * @param playerName The name of the player affected by the rune
     * @param durationStr The duration of the rune notification in seconds
     */
    public static void showRuneNotification(String playerName, String durationStr) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;

        // Parse the duration
        int duration;
        try {
            duration = Integer.parseInt(durationStr);
        } catch (NumberFormatException e) {
            LOGGER.error("Failed to parse duration: {}", durationStr, e);
            duration = 30; // Default to 30 seconds if parsing fails
        }

        // Start the countdown using the custom text renderer
        RuneTextRenderer.startCountdown(playerName, duration);

        LOGGER.info("Started rune notification countdown for player: {}, duration: {}", playerName, duration);
    }

    /**
     * Cleans up resources when the mod is unloaded
     */
    public static void shutdown() {
        // Clear all countdowns
        RuneTextRenderer.clearCountdowns();

        LOGGER.info("TitleManager shutdown complete");
    }
}
