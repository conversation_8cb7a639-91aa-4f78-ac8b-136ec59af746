Write-Host "Building Complex Runes Mod for Minecraft 1.21.1..." -ForegroundColor Green
Write-Host ""

Write-Host "Checking Java version..." -ForegroundColor Yellow
java -version
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Java not found. Please install Java 21+" -ForegroundColor Red
    Read-Host "Press Enter to continue"
    exit 1
}

Write-Host ""
Write-Host "Detecting Java 21 installation..." -ForegroundColor Yellow

# Try common Java 21 installation paths
$javaPaths = @(
    "C:\Program Files\Java\jdk-21.0.7",
    "C:\Program Files\Java\jdk-21",
    "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot",
    "C:\Program Files\Eclipse Adoptium\jdk-21",
    "C:\Program Files\Microsoft\jdk-21",
    "C:\Program Files\Oracle\Java\jdk-21"
)

$javaHome = $null
foreach ($path in $javaPaths) {
    if (Test-Path "$path\bin\java.exe") {
        Write-Host "Found Java 21 at: $path" -ForegroundColor Green
        $javaHome = $path
        break
    }
}

if ($null -eq $javaHome) {
    Write-Host "Could not automatically detect Java 21 installation." -ForegroundColor Red
    Write-Host "Please manually set JAVA_HOME and run gradlew:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Example commands:" -ForegroundColor Cyan
    Write-Host '$env:JAVA_HOME = "C:\Program Files\Java\jdk-21.0.7"' -ForegroundColor White
    Write-Host ".\gradlew clean build" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to continue"
    exit 1
}

# Set JAVA_HOME for this session
$env:JAVA_HOME = $javaHome
Write-Host "Using JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Green
Write-Host ""

Write-Host "Cleaning previous build..." -ForegroundColor Yellow
.\gradlew clean

Write-Host ""
Write-Host "Building mod..." -ForegroundColor Yellow
.\gradlew build

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "================================" -ForegroundColor Green
    Write-Host "✅ BUILD SUCCESS!" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Built files are in: build\libs\" -ForegroundColor Cyan
    Get-ChildItem "build\libs\*.jar" -ErrorAction SilentlyContinue
    Write-Host ""
    Write-Host "Your mod is ready: build\libs\complex-runes-1.0.0.jar" -ForegroundColor Green
    Write-Host ""
    Write-Host "To test the mod, run: .\gradlew runClient" -ForegroundColor Cyan
} else {
    Write-Host ""
    Write-Host "================================" -ForegroundColor Red
    Write-Host "❌ BUILD FAILED!" -ForegroundColor Red
    Write-Host "================================" -ForegroundColor Red
    Write-Host "Please check the error messages above." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to continue"
