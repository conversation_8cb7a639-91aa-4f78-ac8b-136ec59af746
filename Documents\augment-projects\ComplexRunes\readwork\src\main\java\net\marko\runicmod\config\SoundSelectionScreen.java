package net.marko.runicmod.config;

import me.shedaniel.clothconfig2.api.ConfigBuilder;
import me.shedaniel.clothconfig2.api.ConfigCategory;
import me.shedaniel.clothconfig2.api.ConfigEntryBuilder;
import me.shedaniel.clothconfig2.gui.entries.DropdownBoxEntry;
import me.shedaniel.clothconfig2.gui.entries.SelectionListEntry;
import net.marko.runicmod.RunicMod;
import net.marko.runicmod.sound.SoundManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import org.slf4j.Logger;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Screen for selecting sounds
 */
public class SoundSelectionScreen {
    private static final Logger LOGGER = RunicMod.LOGGER;
    
    /**
     * Creates a sound selection screen
     * @param parent The parent screen
     * @return The sound selection screen
     */
    public static Screen create(Screen parent) {
        RunicModConfig config = RunicModConfig.getInstance();
        
        ConfigBuilder builder = ConfigBuilder.create()
                .setParentScreen(parent)
                .setTitle(Text.literal("Sound Selection"));
        
        ConfigEntryBuilder entryBuilder = builder.entryBuilder();
        
        // Minecraft sounds category
        ConfigCategory minecraftCategory = builder.getOrCreateCategory(Text.literal("Minecraft Sounds"));
        
        // Add sound options
        List<SoundOptions.SoundOption> minecraftSounds = SoundOptions.getMinecraftSounds();
        for (SoundOptions.SoundOption sound : minecraftSounds) {
            minecraftCategory.addEntry(entryBuilder.startBooleanToggle(
                    Text.literal(sound.getName()),
                    config.getSelectedSound().equals(sound.getId()) && !config.isUseCustomSound())
                    .setDefaultValue(false)
                    .setSaveConsumer(value -> {
                        if (value) {
                            config.setSelectedSound(sound.getId());
                            config.setUseCustomSound(false);
                            SoundManager.playSound(config.getSoundVolume());
                        }
                    })
                    .build());
        }
        
        // Custom sounds category
        ConfigCategory customCategory = builder.getOrCreateCategory(Text.literal("Custom Sounds"));
        
        // Add button to import sound
        customCategory.addEntry(entryBuilder.startTextDescription(Text.literal("Import a custom sound file (.wav, .ogg, .mp3)"))
                .build());
        
        // Add button to open file chooser
        customCategory.addEntry(entryBuilder.startTextDescription(Text.literal("Click the button below to import a sound file"))
                .build());
        
        customCategory.addEntry(entryBuilder.startTextDescription(Text.literal(""))
                .build());
        
        // We can't add a real button, so we'll use a boolean toggle as a button
        customCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Import Sound File..."),
                false)
                .setDefaultValue(false)
                .setSaveConsumer(value -> {
                    if (value) {
                        importSoundFile(config);
                    }
                })
                .build());
        
        customCategory.addEntry(entryBuilder.startTextDescription(Text.literal(""))
                .build());
        
        // Add custom sound options
        List<SoundOptions.SoundOption> customSounds = SoundOptions.getCustomSounds();
        if (customSounds.isEmpty()) {
            customCategory.addEntry(entryBuilder.startTextDescription(Text.literal("No custom sounds found"))
                    .build());
        } else {
            for (SoundOptions.SoundOption sound : customSounds) {
                customCategory.addEntry(entryBuilder.startBooleanToggle(
                        Text.literal(sound.getName()),
                        config.getSelectedSound().equals(sound.getId()) && config.isUseCustomSound())
                        .setDefaultValue(false)
                        .setSaveConsumer(value -> {
                            if (value) {
                                config.setSelectedSound(sound.getId());
                                config.setUseCustomSound(true);
                                SoundManager.playSound(config.getSoundVolume());
                            }
                        })
                        .build());
            }
        }
        
        // Test category
        ConfigCategory testCategory = builder.getOrCreateCategory(Text.literal("Test"));
        
        // Add current sound info
        testCategory.addEntry(entryBuilder.startTextDescription(
                Text.literal("Current Sound: " + SoundOptions.getDisplayName(config.getSelectedSound())))
                .build());
        
        // Add test button
        testCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Test Sound"),
                false)
                .setDefaultValue(false)
                .setSaveConsumer(value -> {
                    if (value) {
                        SoundManager.playSound(config.getSoundVolume());
                    }
                })
                .build());
        
        // Build the screen
        return builder.build();
    }
    
    /**
     * Opens a file chooser to import a sound file
     */
    private static void importSoundFile(RunicModConfig config) {
        // This needs to run in a separate thread to avoid blocking the game
        new Thread(() -> {
            try {
                // Create a file chooser
                JFileChooser fileChooser = new JFileChooser();
                fileChooser.setDialogTitle("Select Sound File");
                fileChooser.setFileFilter(new FileNameExtensionFilter("Sound Files", "wav", "mp3", "ogg"));
                
                // Show the file chooser
                int result = fileChooser.showOpenDialog(null);
                
                // Process the result
                if (result == JFileChooser.APPROVE_OPTION) {
                    File selectedFile = fileChooser.getSelectedFile();
                    
                    // Import the file
                    String fileName = SoundManager.importSoundFile(selectedFile);
                    
                    // Update the config
                    if (fileName != null) {
                        config.setCustomSoundPath(fileName);
                        config.setUseCustomSound(true);
                        config.setSelectedSound(fileName);
                        
                        // Play the sound
                        MinecraftClient.getInstance().execute(() -> {
                            SoundManager.playSound(config.getSoundVolume());
                        });
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Failed to import sound file", e);
            }
        }).start();
    }
}
