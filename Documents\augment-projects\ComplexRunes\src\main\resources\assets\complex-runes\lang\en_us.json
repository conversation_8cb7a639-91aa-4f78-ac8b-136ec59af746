{"config.complex-runes.title": "Complex Runes Configuration", "config.complex-runes.category.chat": "Chat Monitoring", "config.complex-runes.category.display": "Display Settings", "config.complex-runes.enabled": "Enable Rune Monitoring", "config.complex-runes.enabled.tooltip": "Enable or disable the rune notification system", "config.complex-runes.keywords": "Rune Keywords", "config.complex-runes.keywords.tooltip": "List of keywords to monitor in chat messages", "config.complex-runes.use_regex": "Use Regular Expressions", "config.complex-runes.use_regex.tooltip": "Use regex pattern matching instead of simple keyword matching", "config.complex-runes.regex_pattern": "Regex Pattern", "config.complex-runes.regex_pattern.tooltip": "Regular expression pattern for matching rune messages", "config.complex-runes.case_sensitive": "Case Sensitive", "config.complex-runes.case_sensitive.tooltip": "Whether keyword matching should be case sensitive", "config.complex-runes.show_title": "Show Notifications", "config.complex-runes.show_title.tooltip": "Display rune notifications on screen", "config.complex-runes.show_countdown": "Show Countdown Timer", "config.complex-runes.show_countdown.tooltip": "Display remaining time for each notification", "config.complex-runes.title_x": "Notification X Position", "config.complex-runes.title_x.tooltip": "Horizontal position of notifications (-1 for centered)", "config.complex-runes.title_y": "Notification Y Position", "config.complex-runes.title_y.tooltip": "Vertical position of notifications on screen", "config.complex-runes.title_scale": "Text Scale", "config.complex-runes.title_scale.tooltip": "Scale factor for the notification text", "config.complex-runes.draggable": "Draggable Text", "config.complex-runes.draggable.tooltip": "Allow dragging notifications to reposition them", "config.complex-runes.use_shadow": "Text Shadow", "config.complex-runes.use_shadow.tooltip": "Add shadow effect to notification text"}