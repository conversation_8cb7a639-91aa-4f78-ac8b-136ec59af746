package com.complexrunes.render;

import com.complexrunes.ComplexRunesMod;
import com.complexrunes.config.RuneModConfig;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.util.math.MatrixStack;
import org.slf4j.Logger;

/**
 * A class for handling draggable text on the screen
 */
public class DraggableText {
    private static final Logger LOGGER = ComplexRunesMod.LOGGER;

    // Dragging state
    private static boolean isDragging = false;
    private static int dragOffsetX = 0;
    private static int dragOffsetY = 0;

    // Last known mouse position
    private static int lastMouseX = 0;
    private static int lastMouseY = 0;

    /**
     * Renders text that can be dragged if enabled in the config
     * @param context The draw context
     * @param text The text to render
     * @param config The mod config
     * @return true if the text was clicked/dragged, false otherwise
     */
    public static boolean renderDraggableText(DrawContext context, String text, RuneModConfig config) {
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;

        // Get scale from config
        float scale = config.getTitleScale();

        // Calculate position
        int screenWidth = client.getWindow().getScaledWidth();
        int x = config.getTitleX();
        int y = config.getTitleY();

        // Center the text if x is -1
        if (x == -1) {
            // Calculate the width of the text with scaling
            int textWidth = (int) (textRenderer.getWidth(text) * scale);
            // Center the text on the screen
            x = (screenWidth / 2) - (textWidth / 2);
        }

        // Get color from config
        int color = config.getTitleColor();

        // Save the matrix stack
        MatrixStack matrices = context.getMatrices();
        matrices.push();

        // Apply scale
        matrices.scale(scale, scale, 1.0f);

        // Adjust position for scale (only for non-centered text)
        if (config.getTitleX() != -1) {
            x = (int) (x / scale);
        } else {
            // For centered text, we need to adjust differently
            x = (int) ((screenWidth / 2) / scale) - (textRenderer.getWidth(text) / 2);
        }
        y = (int) (y / scale);

        // Draw the text with or without shadow based on config
        if (config.isUseShadow()) {
            context.drawText(textRenderer, text, x, y, color, true);
        } else {
            context.drawText(textRenderer, text, x, y, color, false);
        }

        // Handle dragging if enabled
        if (config.isDraggable()) {
            // Get mouse position
            int mouseX = (int) (client.mouse.getX() * client.getWindow().getScaledWidth() / client.getWindow().getWidth());
            int mouseY = (int) (client.mouse.getY() * client.getWindow().getScaledHeight() / client.getWindow().getHeight());

            // Store last mouse position
            lastMouseX = mouseX;
            lastMouseY = mouseY;

            // Calculate text bounds
            int textWidth = textRenderer.getWidth(text);
            int textHeight = textRenderer.fontHeight;
            int scaledX = (int) (x * scale);
            int scaledY = (int) (y * scale);
            int scaledWidth = (int) (textWidth * scale);
            int scaledHeight = (int) (textHeight * scale);

            // Check if mouse is over the text
            boolean isMouseOver = mouseX >= scaledX && mouseX <= scaledX + scaledWidth &&
                                 mouseY >= scaledY && mouseY <= scaledY + scaledHeight;

            // Handle mouse input
            if (isMouseOver && client.mouse.wasLeftButtonClicked() && !isDragging) {
                // Start dragging
                isDragging = true;
                dragOffsetX = mouseX - scaledX;
                dragOffsetY = mouseY - scaledY;
                LOGGER.debug("Started dragging text at ({}, {})", mouseX, mouseY);
                return true;
            } else if (isDragging && client.mouse.wasLeftButtonClicked()) {
                // Continue dragging
                int newX = mouseX - dragOffsetX;
                int newY = mouseY - dragOffsetY;

                // Update config
                config.setTitleX((int) (newX / scale));
                config.setTitleY((int) (newY / scale));

                LOGGER.debug("Dragged text to ({}, {})", newX, newY);
                return true;
            } else if (isDragging && !client.mouse.wasLeftButtonClicked()) {
                // Stop dragging
                isDragging = false;
                LOGGER.debug("Stopped dragging text");
                return false;
            }
        }

        // Restore the matrix stack
        matrices.pop();

        return false;
    }

    /**
     * Resets the dragging state
     */
    public static void resetDragging() {
        isDragging = false;
    }
}
