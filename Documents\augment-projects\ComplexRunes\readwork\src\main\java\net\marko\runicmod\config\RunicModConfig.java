package net.marko.runicmod.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.fabricmc.loader.api.FabricLoader;
import net.marko.runicmod.RunicMod;
import org.slf4j.Logger;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

/**
 * Configuration class for the Runic Mod
 */
public class RunicModConfig {
    private static final Logger LOGGER = RunicMod.LOGGER;
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final File CONFIG_FILE = FabricLoader.getInstance().getConfigDir().resolve("runic-mod.json").toFile();

    private static RunicModConfig INSTANCE;

    // Display settings
    private int titleX = -1; // -1 = center
    private int titleY = 20; // pixels from top
    private float titleScale = 1.0f;
    private int titleColor = 0xFF0000; // Red
    private String titleColorOption = "RED"; // Default color option
    private int customTitleColor = 0xFF0000; // Custom title color
    private boolean useCustomTitleColor = false; // Whether to use custom title color
    private boolean showTitle = true; // Whether to show the title
    private boolean isDraggable = true; // Whether the text can be dragged

    // Sound settings
    private boolean playSounds = true;
    private float soundVolume = 1.0f;
    private String selectedSound = "minecraft:entity.experience_orb.pickup"; // Default sound
    private boolean useCustomSound = false;
    private String customSoundPath = ""; // Path to custom sound file

    // Glow settings
    private int glowColor = 0xFF0000; // Red
    private String glowColorOption = "RED"; // Default color option
    private int customGlowColor = 0xFF0000; // Custom glow color
    private boolean useCustomGlowColor = false; // Whether to use custom glow color
    private boolean showGlow = true; // Whether to show the glow effect

    // Distance settings
    private int maxDistance = 25; // blocks

    /**
     * Gets the singleton instance of the config
     */
    public static RunicModConfig getInstance() {
        if (INSTANCE == null) {
            INSTANCE = loadConfig();
        }
        return INSTANCE;
    }

    /**
     * Loads the config from file
     */
    private static RunicModConfig loadConfig() {
        if (CONFIG_FILE.exists()) {
            try (FileReader reader = new FileReader(CONFIG_FILE)) {
                return GSON.fromJson(reader, RunicModConfig.class);
            } catch (IOException e) {
                LOGGER.error("Failed to load config", e);
            }
        }

        // If the file doesn't exist or there was an error, create a default config
        RunicModConfig config = new RunicModConfig();
        config.saveConfig();
        return config;
    }

    /**
     * Saves the config to file
     */
    public void saveConfig() {
        try {
            if (!CONFIG_FILE.exists()) {
                CONFIG_FILE.getParentFile().mkdirs();
                CONFIG_FILE.createNewFile();
            }

            try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
                GSON.toJson(this, writer);
            }
        } catch (IOException e) {
            LOGGER.error("Failed to save config", e);
        }
    }

    // Getters and setters

    public int getTitleX() {
        return titleX;
    }

    public void setTitleX(int titleX) {
        this.titleX = titleX;
        saveConfig();
    }

    public int getTitleY() {
        return titleY;
    }

    public void setTitleY(int titleY) {
        this.titleY = titleY;
        saveConfig();
    }

    public float getTitleScale() {
        return titleScale;
    }

    public void setTitleScale(float titleScale) {
        this.titleScale = titleScale;
        saveConfig();
    }

    public int getTitleColor() {
        if (useCustomTitleColor) {
            return customTitleColor;
        } else if (ColorOption.fromName(titleColorOption) == ColorOption.CUSTOM) {
            return customTitleColor;
        } else {
            return ColorOption.fromName(titleColorOption).getColor();
        }
    }

    public void setTitleColor(int titleColor) {
        ColorOption option = ColorOption.fromColor(titleColor);
        if (option == ColorOption.CUSTOM) {
            this.customTitleColor = titleColor;
            this.titleColorOption = "CUSTOM";
        } else {
            this.titleColorOption = option.name();
        }
        this.titleColor = titleColor;
        saveConfig();
    }

    public String getTitleColorOption() {
        return titleColorOption;
    }

    public void setTitleColorOption(String titleColorOption) {
        this.titleColorOption = titleColorOption;
        if (ColorOption.fromName(titleColorOption) != ColorOption.CUSTOM) {
            this.titleColor = ColorOption.fromName(titleColorOption).getColor();
        }
        saveConfig();
    }

    public int getCustomTitleColor() {
        return customTitleColor;
    }

    public void setCustomTitleColor(int customTitleColor) {
        this.customTitleColor = customTitleColor;
        if (useCustomTitleColor || ColorOption.fromName(titleColorOption) == ColorOption.CUSTOM) {
            this.titleColor = customTitleColor;
        }
        saveConfig();
    }

    public boolean isUseCustomTitleColor() {
        return useCustomTitleColor;
    }

    public void setUseCustomTitleColor(boolean useCustomTitleColor) {
        this.useCustomTitleColor = useCustomTitleColor;
        if (useCustomTitleColor) {
            this.titleColor = customTitleColor;
        } else {
            if (ColorOption.fromName(titleColorOption) != ColorOption.CUSTOM) {
                this.titleColor = ColorOption.fromName(titleColorOption).getColor();
            }
        }
        saveConfig();
    }

    public boolean isDraggable() {
        return isDraggable;
    }

    public void setDraggable(boolean draggable) {
        this.isDraggable = draggable;
        saveConfig();
    }

    public boolean isPlaySounds() {
        return playSounds;
    }

    public void setPlaySounds(boolean playSounds) {
        this.playSounds = playSounds;
        saveConfig();
    }

    public float getSoundVolume() {
        return soundVolume;
    }

    public void setSoundVolume(float soundVolume) {
        this.soundVolume = soundVolume;
        saveConfig();
    }

    public String getSelectedSound() {
        return selectedSound;
    }

    public void setSelectedSound(String selectedSound) {
        this.selectedSound = selectedSound;
        saveConfig();
    }

    public boolean isUseCustomSound() {
        return useCustomSound;
    }

    public void setUseCustomSound(boolean useCustomSound) {
        this.useCustomSound = useCustomSound;
        saveConfig();
    }

    public String getCustomSoundPath() {
        return customSoundPath;
    }

    public void setCustomSoundPath(String customSoundPath) {
        this.customSoundPath = customSoundPath;
        saveConfig();
    }

    public int getGlowColor() {
        if (useCustomGlowColor) {
            return customGlowColor;
        } else if (ColorOption.fromName(glowColorOption) == ColorOption.CUSTOM) {
            return customGlowColor;
        } else {
            return ColorOption.fromName(glowColorOption).getColor();
        }
    }

    public void setGlowColor(int glowColor) {
        ColorOption option = ColorOption.fromColor(glowColor);
        if (option == ColorOption.CUSTOM) {
            this.customGlowColor = glowColor;
            this.glowColorOption = "CUSTOM";
        } else {
            this.glowColorOption = option.name();
        }
        this.glowColor = glowColor;
        saveConfig();
    }

    public String getGlowColorOption() {
        return glowColorOption;
    }

    public void setGlowColorOption(String glowColorOption) {
        this.glowColorOption = glowColorOption;
        if (ColorOption.fromName(glowColorOption) != ColorOption.CUSTOM) {
            this.glowColor = ColorOption.fromName(glowColorOption).getColor();
        }
        saveConfig();
    }

    public int getCustomGlowColor() {
        return customGlowColor;
    }

    public void setCustomGlowColor(int customGlowColor) {
        this.customGlowColor = customGlowColor;
        if (useCustomGlowColor || ColorOption.fromName(glowColorOption) == ColorOption.CUSTOM) {
            this.glowColor = customGlowColor;
        }
        saveConfig();
    }

    public boolean isUseCustomGlowColor() {
        return useCustomGlowColor;
    }

    public void setUseCustomGlowColor(boolean useCustomGlowColor) {
        this.useCustomGlowColor = useCustomGlowColor;
        if (useCustomGlowColor) {
            this.glowColor = customGlowColor;
        } else {
            if (ColorOption.fromName(glowColorOption) != ColorOption.CUSTOM) {
                this.glowColor = ColorOption.fromName(glowColorOption).getColor();
            }
        }
        saveConfig();
    }

    public int getMaxDistance() {
        return maxDistance;
    }

    public void setMaxDistance(int maxDistance) {
        this.maxDistance = maxDistance;
        saveConfig();
    }

    public boolean isShowTitle() {
        return showTitle;
    }

    public void setShowTitle(boolean showTitle) {
        this.showTitle = showTitle;
        saveConfig();
    }

    public boolean isShowGlow() {
        return showGlow;
    }

    public void setShowGlow(boolean showGlow) {
        this.showGlow = showGlow;
        saveConfig();
    }
}
