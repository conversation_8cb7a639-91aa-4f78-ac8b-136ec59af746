package net.marko.runicmod;

import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.render.CustomTextRenderer;
import net.minecraft.client.MinecraftClient;
import org.slf4j.Logger;

/**
 * Manages title displays for runic obstructions
 */
public class TitleManager {
    private static final Logger LOGGER = RunicMod.LOGGER;

    /**
     * Shows a runic title for a player
     * @param playerName The name of the player affected by runic obstruction
     * @param durationStr The duration of the runic obstruction in seconds
     */
    public static void showRunicTitle(String playerName, String durationStr) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;

        // Parse the duration
        int duration;
        try {
            duration = Integer.parseInt(durationStr);
        } catch (NumberFormatException e) {
            LOGGER.error("Failed to parse duration: {}", durationStr, e);
            duration = 5; // Default to 5 seconds if parsing fails
        }

        // Start the countdown using the custom text renderer
        CustomTextRenderer.startCountdown(playerName, duration);

        LOGGER.info("Started runic countdown for player: {}, duration: {}", playerName, duration);
    }

    /**
     * Cleans up resources when the mod is unloaded
     */
    public static void shutdown() {
        // Clear all countdowns
        CustomTextRenderer.clearCountdowns();

        LOGGER.info("TitleManager shutdown complete");
    }
}
