package com.complexrunes.render;

import com.complexrunes.ComplexRunesMod;
import com.complexrunes.config.RuneModConfig;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.text.Text;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * Custom text renderer for displaying rune notifications
 */
public class RuneTextRenderer {
    private static final Logger LOGGER = ComplexRunesMod.LOGGER;

    // Map of player names to their countdown information
    private static final Map<String, CountdownInfo> countdowns = new HashMap<>();

    /**
     * Starts a countdown for a player
     * @param playerName The name of the player
     * @param duration The duration of the countdown in seconds
     */
    public static void startCountdown(String playerName, int duration) {
        long endTime = System.currentTimeMillis() + (duration * 1000L);
        countdowns.put(playerName, new CountdownInfo(endTime, duration));
        LOGGER.info("Started rune notification countdown for player: {}, duration: {}", playerName, duration);
    }

    /**
     * Renders all active countdowns
     * @param context The draw context
     */
    public static void renderCountdowns(DrawContext context) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;

        // Get the config
        RuneModConfig config = RuneModConfig.getInstance();

        // Don't render if disabled
        if (!config.isEnabled() || !config.isShowTitle()) return;

        // Get the current time
        long currentTime = System.currentTimeMillis();

        // Remove expired countdowns and render active ones
        countdowns.entrySet().removeIf(entry -> {
            String playerName = entry.getKey();
            CountdownInfo info = entry.getValue();

            // Check if the countdown has expired
            if (currentTime > info.endTime) {
                LOGGER.debug("Countdown expired for player: {}", playerName);
                return true; // Remove this entry
            }

            // Calculate remaining time
            int remainingSeconds = (int) ((info.endTime - currentTime) / 1000) + 1;

            // Render the countdown
            renderCountdown(context, playerName, remainingSeconds, config);

            return false; // Keep this entry
        });
    }

    /**
     * Renders a countdown for a player
     * @param context The draw context
     * @param playerName The name of the player
     * @param remainingSeconds The remaining time in seconds
     * @param config The mod config
     */
    private static void renderCountdown(DrawContext context, String playerName, int remainingSeconds, RuneModConfig config) {
        // Format the text based on configuration
        String text;
        if (config.isShowCountdown()) {
            text = playerName + " RUNE ACTIVE - " + remainingSeconds + "s";
        } else {
            text = playerName + " RUNE ACTIVE";
        }

        // Use the draggable text renderer
        DraggableText.renderDraggableText(context, text, config);
    }

    /**
     * Clears all countdowns
     */
    public static void clearCountdowns() {
        countdowns.clear();
        LOGGER.info("Cleared all rune notification countdowns");
    }

    /**
     * Gets the number of active countdowns
     */
    public static int getActiveCountdownCount() {
        return countdowns.size();
    }

    /**
     * Information about a countdown
     */
    private static class CountdownInfo {
        private final long endTime;
        private final int originalDuration;

        public CountdownInfo(long endTime, int originalDuration) {
            this.endTime = endTime;
            this.originalDuration = originalDuration;
        }
    }
}
