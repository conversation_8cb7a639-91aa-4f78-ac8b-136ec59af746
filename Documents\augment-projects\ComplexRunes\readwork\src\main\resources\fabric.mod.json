{"schemaVersion": 1, "id": "runic-mod", "version": "${version}", "name": "Runic mod", "description": "A mod that detects and processes rune-related chat messages", "authors": ["Cerv and apl_ (and chatgpt <3)"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/runic-mod/icon.png", "environment": "client", "entrypoints": {"client": ["net.marko.runicmod.RunicMod"], "modmenu": ["net.marko.runicmod.config.RunicModMenuIntegration"]}, "mixins": ["runic-mod.mixins.json"], "depends": {"fabricloader": ">=0.16.12", "minecraft": "~1.21.1", "java": ">=21", "fabric-api": "*"}, "suggests": {"modmenu": "*"}}