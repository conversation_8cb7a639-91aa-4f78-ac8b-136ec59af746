package net.marko.runicmod.render;

import net.marko.runicmod.RunicMod;
import net.marko.runicmod.config.RunicModConfig;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.text.Text;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * Custom text renderer for displaying runic titles
 */
public class CustomTextRenderer {
    private static final Logger LOGGER = RunicMod.LOGGER;

    // Map of player names to their countdown information
    private static final Map<String, CountdownInfo> countdowns = new HashMap<>();

    /**
     * Starts a countdown for a player
     * @param playerName The name of the player
     * @param duration The duration of the countdown in seconds
     */
    public static void startCountdown(String playerName, int duration) {
        long endTime = System.currentTimeMillis() + (duration * 1000L);
        countdowns.put(playerName, new CountdownInfo(endTime, duration));
        LOGGER.info("Started custom text countdown for player: {}, duration: {}", playerName, duration);
    }

    /**
     * Renders all active countdowns
     * @param context The draw context
     */
    public static void renderCountdowns(DrawContext context) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;

        // Get the config
        RunicModConfig config = RunicModConfig.getInstance();

        // Get the current time
        long currentTime = System.currentTimeMillis();

        // Remove expired countdowns and render active ones
        countdowns.entrySet().removeIf(entry -> {
            String playerName = entry.getKey();
            CountdownInfo info = entry.getValue();

            // Check if the countdown has expired
            if (currentTime > info.endTime) {
                return true; // Remove this entry
            }

            // Calculate remaining time
            int remainingSeconds = (int) ((info.endTime - currentTime) / 1000) + 1;

            // Render the countdown
            renderCountdown(context, playerName, remainingSeconds, config);

            return false; // Keep this entry
        });
    }

    /**
     * Renders a countdown for a player
     * @param context The draw context
     * @param playerName The name of the player
     * @param remainingSeconds The remaining time in seconds
     * @param config The mod config
     */
    private static void renderCountdown(DrawContext context, String playerName, int remainingSeconds, RunicModConfig config) {
        // Format the text
        String text = playerName + " IS RUNICED FOR " + remainingSeconds;

        // Use the draggable text renderer
        DraggableText.renderDraggableText(context, text, config);
    }

    /**
     * Clears all countdowns
     */
    public static void clearCountdowns() {
        countdowns.clear();
        LOGGER.info("Cleared all custom text countdowns");
    }

    /**
     * Information about a countdown
     */
    private static class CountdownInfo {
        private final long endTime;
        private final int originalDuration;

        public CountdownInfo(long endTime, int originalDuration) {
            this.endTime = endTime;
            this.originalDuration = originalDuration;
        }
    }
}
