package net.marko.runicmod.config;

import net.minecraft.text.Text;

/**
 * Enum for color options
 */
public enum ColorOption {
    RED("Red", 0xFF0000),
    GREEN("Green", 0x00FF00),
    BLUE("Blue", 0x0000FF),
    YELLOW("Yellow", 0xFFFF00),
    CYAN("Cyan", 0x00FFFF),
    MAGENTA("Magenta", 0xFF00FF),
    ORANGE("Orange", 0xFF8000),
    PURPLE("Purple", 0x8000FF),
    PINK("Pink", 0xFF80FF),
    WHITE("White", 0xFFFFFF),
    BLACK("Black", 0x000000),
    GRAY("Gray", 0x808080),
    CUSTOM("Custom", -1); // -1 indicates custom color

    private final String name;
    private final int color;

    ColorOption(String name, int color) {
        this.name = name;
        this.color = color;
    }

    public String getName() {
        return name;
    }

    public int getColor() {
        return color;
    }

    public Text getText() {
        return Text.literal(name);
    }

    /**
     * Gets a ColorOption by its color value
     * @param color The color value
     * @return The matching ColorOption, or CUSTOM if no match is found
     */
    public static ColorOption fromColor(int color) {
        for (ColorOption option : values()) {
            if (option.color == color) {
                return option;
            }
        }
        return CUSTOM;
    }

    /**
     * Gets a ColorOption by its name
     * @param name The name of the color
     * @return The matching ColorOption, or CUSTOM if no match is found
     */
    public static ColorOption fromName(String name) {
        for (ColorOption option : values()) {
            if (option.name.equalsIgnoreCase(name)) {
                return option;
            }
        }
        return CUSTOM;
    }
}
