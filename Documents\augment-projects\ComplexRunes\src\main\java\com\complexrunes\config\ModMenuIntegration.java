package com.complexrunes.config;

import com.terraformersmc.modmenu.api.ConfigScreenFactory;
import com.terraformersmc.modmenu.api.ModMenuApi;
import me.shedaniel.clothconfig2.api.ConfigBuilder;
import me.shedaniel.clothconfig2.api.ConfigCategory;
import me.shedaniel.clothconfig2.api.ConfigEntryBuilder;
import net.minecraft.text.Text;

import java.util.Arrays;

public class ModMenuIntegration implements ModMenuApi {
    
    @Override
    public ConfigScreenFactory<?> getModConfigScreenFactory() {
        return parent -> {
            RuneModConfig config = RuneModConfig.getInstance();
            
            ConfigBuilder builder = ConfigBuilder.create()
                .setParentScreen(parent)
                .setTitle(Text.translatable("config.complex-runes.title"))
                .setSavingRunnable(config::save);
            
            ConfigEntryBuilder entryBuilder = builder.entryBuilder();
            
            // Chat Monitoring Category
            ConfigCategory chatCategory = builder.getOrCreateCategory(Text.translatable("config.complex-runes.category.chat"));
            
            chatCategory.addEntry(entryBuilder.startBooleanToggle(Text.translatable("config.complex-runes.enabled"), config.isEnabled())
                .setDefaultValue(true)
                .setSaveConsumer(config::setEnabled)
                .build());
            
            chatCategory.addEntry(entryBuilder.startStrList(Text.translatable("config.complex-runes.keywords"), config.getRuneKeywords())
                .setDefaultValue(Arrays.asList("rune", "Rune", "RUNE"))
                .setSaveConsumer(config::setRuneKeywords)
                .build());
            
            chatCategory.addEntry(entryBuilder.startBooleanToggle(Text.translatable("config.complex-runes.use_regex"), config.isUseRegex())
                .setDefaultValue(false)
                .setSaveConsumer(config::setUseRegex)
                .build());
            
            chatCategory.addEntry(entryBuilder.startStrField(Text.translatable("config.complex-runes.regex_pattern"), config.getCustomRegexPattern())
                .setDefaultValue(".*[Rr]une.*")
                .setSaveConsumer(config::setCustomRegexPattern)
                .build());
            
            chatCategory.addEntry(entryBuilder.startBooleanToggle(Text.translatable("config.complex-runes.case_sensitive"), config.isCaseSensitive())
                .setDefaultValue(false)
                .setSaveConsumer(config::setCaseSensitive)
                .build());
            
            // Display Category
            ConfigCategory displayCategory = builder.getOrCreateCategory(Text.translatable("config.complex-runes.category.display"));
            
            displayCategory.addEntry(entryBuilder.startBooleanToggle(Text.translatable("config.complex-runes.show_title"), config.isShowTitle())
                .setDefaultValue(true)
                .setSaveConsumer(config::setShowTitle)
                .build());
            
            displayCategory.addEntry(entryBuilder.startBooleanToggle(Text.translatable("config.complex-runes.show_countdown"), config.isShowCountdown())
                .setDefaultValue(true)
                .setSaveConsumer(config::setShowCountdown)
                .build());
            
            displayCategory.addEntry(entryBuilder.startIntField(Text.translatable("config.complex-runes.title_x"), config.getTitleX())
                .setDefaultValue(-1)
                .setSaveConsumer(config::setTitleX)
                .build());
            
            displayCategory.addEntry(entryBuilder.startIntField(Text.translatable("config.complex-runes.title_y"), config.getTitleY())
                .setDefaultValue(50)
                .setMin(0)
                .setMax(2000)
                .setSaveConsumer(config::setTitleY)
                .build());
            
            displayCategory.addEntry(entryBuilder.startFloatField(Text.translatable("config.complex-runes.title_scale"), config.getTitleScale())
                .setDefaultValue(1.0f)
                .setMin(0.1f)
                .setMax(5.0f)
                .setSaveConsumer(config::setTitleScale)
                .build());
            
            displayCategory.addEntry(entryBuilder.startBooleanToggle(Text.translatable("config.complex-runes.draggable"), config.isDraggable())
                .setDefaultValue(false)
                .setSaveConsumer(config::setDraggable)
                .build());
            
            displayCategory.addEntry(entryBuilder.startBooleanToggle(Text.translatable("config.complex-runes.use_shadow"), config.isUseShadow())
                .setDefaultValue(true)
                .setSaveConsumer(config::setUseShadow)
                .build());
            
            return builder.build();
        };
    }
}
