@echo off
echo Building Complex Runes Mod for Minecraft 1.21.1...
echo.

echo Checking Java version...
java -version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found. Please install Java 21+
    pause
    exit /b 1
)

echo.
echo Cleaning previous build...
call gradlew clean

echo.
echo Building mod...
call gradlew build

if %ERRORLEVEL% equ 0 (
    echo.
    echo ================================
    echo Build completed successfully!
    echo ================================
    echo.
    echo Built files are in: build\libs\
    dir build\libs\*.jar
    echo.
    echo To test the mod, run: gradlew runClient
) else (
    echo.
    echo ================================
    echo Build failed!
    echo ================================
    echo Please check the error messages above.
)

pause
