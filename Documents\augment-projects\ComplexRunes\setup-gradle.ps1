Write-Host "Setting up Gradle Wrapper for Complex Runes Mod..." -ForegroundColor Green
Write-Host ""

# Create gradle wrapper directory if it doesn't exist
$wrapperDir = "gradle\wrapper"
if (!(Test-Path $wrapperDir)) {
    Write-Host "Creating gradle wrapper directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $wrapperDir -Force | Out-Null
}

# Download gradle-wrapper.jar
$wrapperJarUrl = "https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar"
$wrapperJarPath = "$wrapperDir\gradle-wrapper.jar"

Write-Host "Downloading Gradle wrapper JAR..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $wrapperJarUrl -OutFile $wrapperJarPath -UseBasicParsing
    Write-Host "✅ Downloaded gradle-wrapper.jar" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to download gradle-wrapper.jar" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual solution:" -ForegroundColor Yellow
    Write-Host "1. Download gradle-wrapper.jar from: $wrapperJarUrl" -ForegroundColor Cyan
    Write-Host "2. Save it to: $wrapperJarPath" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "Press Enter to continue"
    exit 1
}

# Verify the file was downloaded
if (Test-Path $wrapperJarPath) {
    $fileSize = (Get-Item $wrapperJarPath).Length
    Write-Host "✅ Gradle wrapper JAR downloaded successfully ($fileSize bytes)" -ForegroundColor Green
} else {
    Write-Host "❌ Gradle wrapper JAR not found after download" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Gradle wrapper setup complete!" -ForegroundColor Green
Write-Host "Now you can build the mod with: .\build.ps1" -ForegroundColor Cyan
Write-Host ""

# Ask if user wants to build now
$buildNow = Read-Host "Do you want to build the mod now? (y/n)"
if ($buildNow -eq "y" -or $buildNow -eq "Y" -or $buildNow -eq "yes") {
    Write-Host ""
    Write-Host "Starting build..." -ForegroundColor Green
    .\build.ps1
}
