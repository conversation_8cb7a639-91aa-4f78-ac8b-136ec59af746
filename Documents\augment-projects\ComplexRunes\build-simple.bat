@echo off
echo Simple Build for Complex Runes Mod...
echo.

REM Try the most common Java 21 installation paths one by one

echo Trying: C:\Program Files\Java\jdk-21.0.7
set JAVA_HOME=C:\Program Files\Java\jdk-21.0.7
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Found Java 21 at: %JAVA_HOME%
    goto build
)

echo Trying: C:\Program Files\Java\jdk-21
set JAVA_HOME=C:\Program Files\Java\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Found Java 21 at: %JAVA_HOME%
    goto build
)

echo Trying: C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Found Java 21 at: %JAVA_HOME%
    goto build
)

echo Trying: C:\Program Files\Eclipse Adoptium\jdk-21
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Found Java 21 at: %JAVA_HOME%
    goto build
)

echo Trying: C:\Program Files\Microsoft\jdk-21
set JAVA_HOME=C:\Program Files\Microsoft\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Found Java 21 at: %JAVA_HOME%
    goto build
)

echo Trying: C:\Program Files\Oracle\Java\jdk-21
set JAVA_HOME=C:\Program Files\Oracle\Java\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Found Java 21 at: %JAVA_HOME%
    goto build
)

echo.
echo ================================
echo Could not find Java 21 automatically!
echo ================================
echo.
echo Please run: .\find-java.bat
echo This will help you locate your Java 21 installation.
echo.
echo Then manually run one of these commands:
echo set JAVA_HOME=[YOUR_JAVA_21_PATH] ^& gradlew clean build
echo.
echo For example:
echo set JAVA_HOME=C:\Program Files\Java\jdk-21 ^& gradlew clean build
echo.
pause
exit /b 1

:build
echo.
echo Using JAVA_HOME: %JAVA_HOME%
echo Building mod...
echo.

gradlew clean build

if %ERRORLEVEL% equ 0 (
    echo.
    echo ================================
    echo ✅ BUILD SUCCESS!
    echo ================================
    echo.
    echo Your mod is ready at: build\libs\complex-runes-1.0.0.jar
    echo.
    echo To test the mod, run: gradlew runClient
    echo.
) else (
    echo.
    echo ================================
    echo ❌ BUILD FAILED!
    echo ================================
    echo Check the error messages above.
    echo.
)

pause
