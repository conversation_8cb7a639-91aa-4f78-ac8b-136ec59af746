info: [2025-05-22T07:12:23Z INFO  rust_launcher::start::bindings] server: None
info: [2025-05-22T07:12:23Z INFO  rust_launcher::start] Loading version
info: {"versionSize":222564743,"state":{"VersionLoading":true},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":0}
info: [2025-05-22T07:12:23Z INFO  rust_launcher::start] Finished moving mods
info: [2025-05-22T07:12:23Z INFO  rust_launcher::start] Finished loading version
info: [2025-05-22T07:12:23Z INFO  rust_launcher::start] Preparing assets
[2025-05-22T07:12:23Z INFO  rust_launcher::start] Preparing JRE
info: {"versionSize":222564743,"state":{"Downloading":["Libraries","Jre","Mods","Assets"]},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":35518}
info: [2025-05-22T07:12:23Z INFO  rust_launcher::start] Preparing libraries
info: [2025-05-22T07:12:23Z INFO  rust_launcher::start] Preparing mods
info: {"versionSize":222564743,"state":{"Downloading":["Libraries","Mods","Assets"]},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":48807807}
info: {"versionSize":222564743,"state":{"Downloading":["Libraries","Mods","Assets"]},"currentDownloadRate":413054,"eta":"less than a minute","expectedTasks":6,"downloadSpeed":413054,"downloaded":239674275}
info: [2025-05-22T07:12:24Z INFO  rust_launcher::start] Finished preparing mods
info: {"versionSize":222564743,"state":{"Downloading":["Libraries","Assets"]},"currentDownloadRate":413054,"eta":"30 minutes","expectedTasks":6,"downloadSpeed":413054,"downloaded":967433703}
info: [2025-05-22T07:12:24Z INFO  rust_launcher::start] Finished preparing libraries
info: {"versionSize":222564743,"state":{"Downloading":["Assets"]},"currentDownloadRate":413054,"eta":"34 minutes","expectedTasks":6,"downloadSpeed":413054,"downloaded":1062612182}
info: [2025-05-22T07:12:24Z INFO  rust_launcher::start] Finished preparing assets
[2025-05-22T07:12:24Z INFO  rust_launcher::start] Preparing registry
info: {"versionSize":222564743,"state":{"Jvm":true},"currentDownloadRate":413054,"eta":"35 minutes","expectedTasks":6,"downloadSpeed":413054,"downloaded":**********}
info: [2025-05-22T07:12:24Z INFO  rust_launcher::start] Finished preparing registry
info: [2025-05-22T07:12:24Z INFO  rust_launcher::common::launch] starting JVM with arguments: ["-Djava.library.path=libraries/native\\net/digitalingot/fcef/0.1.1\\extracted/;libraries/native\\net/digitalingot/fwebp/0.0.2\\extracted/;libraries/native\\net/digitalingot/favif/0.0.1\\extracted/;libraries/native\\com/discord/discord-game-sdk/3.2.1\\extracted/;libraries/native\\net/digitalingot/fdiscord/0.0.1\\extracted/;libraries/native\\net/digitalingot/fjni/0.0.2\\extracted/;libraries/native\\net/digitalingot/cef_binary/103.0.0\\extracted/;libraries/native\\org/jitsi/libjitsi-opus-native/1.1-32-g2a5a8171\\extracted/;libraries/native\\org/lwjgl/lwjgl-freetype/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-glfw/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-jemalloc/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-openal/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-opengl/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-stb/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-tinyfd/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl/3.3.3\\extracted/", "--add-opens=java.desktop/java.awt.event=ALL-UNNAMED", "--add-opens=java.desktop/java.awt.color=ALL-UNNAMED", "--add-opens=java.desktop/java.awt=ALL-UNNAMED", "--add-opens=java.base/java.lang=ALL-UNNAMED", "-Xmx15183M", "-XX:+UnlockExperimentalVMOptions", "-XX:+UseG1GC", "-XX:G1NewSizePercent=20", "-XX:G1ReservePercent=20", "-XX:MaxGCPauseMillis=50", "-XX:G1HeapRegionSize=32M", "-Dlog4j2.formatMsgNoLookups=true", "-XX:ErrorFile=feather/java_error.log", "-Djavax.accessibility.assistive_technologies=", "-Djavax.net.ssl.trustStoreType=WINDOWS-ROOT", "-Dfeather.overrideHardwareAccel=false", "-cp", "libraries\\java\\net/minecraft/client/1.21.1/minecraft-1.21.1.jar;libraries\\java\\com/github/oshi/oshi-core/6.4.10/oshi-core-6.4.10.jar;libraries\\java\\com/google/code/gson/gson/2.10.1/gson-2.10.1.jar;libraries\\java\\com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar;libraries\\java\\com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar;libraries\\java\\com/ibm/icu/icu4j/73.2/icu4j-73.2.jar;libraries\\java\\com/mojang/authlib/6.0.54/authlib-6.0.54.jar;libraries\\java\\com/mojang/blocklist/1.0.10/blocklist-1.0.10.jar;libraries\\java\\com/mojang/brigadier/1.3.10/brigadier-1.3.10.jar;libraries\\java\\com/mojang/datafixerupper/8.0.16/datafixerupper-8.0.16.jar;libraries\\java\\com/mojang/logging/1.2.7/logging-1.2.7.jar;libraries\\java\\com/mojang/patchy/2.2.10/patchy-2.2.10.jar;libraries\\java\\com/mojang/text2speech/1.17.9/text2speech-1.17.9.jar;libraries\\java\\commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar;libraries\\java\\commons-io/commons-io/2.15.1/commons-io-2.15.1.jar;libraries\\java\\commons-logging/commons-logging/1.2/commons-logging-1.2.jar;libraries\\java\\io/netty/netty-buffer/4.1.97.Final/netty-buffer-4.1.97.Final.jar;libraries\\java\\io/netty/netty-codec/4.1.97.Final/netty-codec-4.1.97.Final.jar;libraries\\java\\io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar;libraries\\java\\io/netty/netty-handler/4.1.97.Final/netty-handler-4.1.97.Final.jar;libraries\\java\\io/netty/netty-resolver/4.1.97.Final/netty-resolver-4.1.97.Final.jar;libraries\\java\\io/netty/netty-transport-classes-epoll/4.1.97.Final/netty-transport-classes-epoll-4.1.97.Final.jar;libraries\\java\\io/netty/netty-transport-native-unix-common/4.1.97.Final/netty-transport-native-unix-common-4.1.97.Final.jar;libraries\\java\\io/netty/netty-transport/4.1.97.Final/netty-transport-4.1.97.Final.jar;libraries\\java\\it/unimi/dsi/fastutil/8.5.12/fastutil-8.5.12.jar;libraries\\java\\net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar;libraries\\java\\net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar;libraries\\java\\net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar;libraries\\java\\org/apache/commons/commons-compress/1.26.0/commons-compress-1.26.0.jar;libraries\\java\\org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar;libraries\\java\\org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar;libraries\\java\\org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar;libraries\\java\\org/apache/logging/log4j/log4j-api/2.22.1/log4j-api-2.22.1.jar;libraries\\java\\org/apache/logging/log4j/log4j-core/2.22.1/log4j-core-2.22.1.jar;libraries\\java\\org/apache/logging/log4j/log4j-slf4j2-impl/2.22.1/log4j-slf4j2-impl-2.22.1.jar;libraries\\java\\org/jcraft/jorbis/0.0.17/jorbis-0.0.17.jar;libraries\\java\\org/joml/joml/1.10.5/joml-1.10.5.jar;libraries\\java\\org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3.jar;libraries\\java\\org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar;libraries\\java\\org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar;libraries\\java\\org/ow2/asm/asm/9.7.1/asm-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar;libraries\\java\\net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar;libraries/java\\net/fabricmc/fabric-loader/0.16.14/fabric-loader-0.16.14.jar;libraries\\java\\net/fabricmc/intermediary/1.21.1/intermediary-1.21.1.jar;libraries\\java\\net/digitalingot/rust-extension/1.0.10/rust-extension-1.0.10.jar;libraries\\java\\net/digitalingot/fjni/0.0.2/fjni-0.0.2.jar;libraries\\java\\net/digitalingot/fdiscord/0.0.1/fdiscord-0.0.1.jar;libraries\\java\\net/digitalingot/fcef/0.1.1/fcef-0.1.1.jar;libraries\\java\\net/digitalingot/fwebp/0.0.1/fwebp-0.0.1.jar;libraries\\java\\net/digitalingot/favif/0.0.1/favif-0.0.1.jar;libraries\\java\\net/digitalingot/feather-server-api/messaging/0.0.5/messaging-0.0.5.jar;libraries\\java\\org/jitsi/libjitsi-opus/1.1-32-g2a5a8171/libjitsi-opus-1.1-32-g2a5a8171.jar;libraries\\java\\org/capnproto/runtime/0.1.10/runtime-0.1.10.jar;libraries\\java\\com/google/inject/guice/5.1.1/guice-5.1.1.jar;libraries\\java\\javassist/javassist/3.12.1.GA/javassist-3.12.1.GA.jar;libraries\\java\\io/sentry/sentry/7.18.1/sentry-7.18.1.jar;libraries\\java\\io/netty/netty-codec-http/4.1.97.Final/netty-codec-http-4.1.97.Final.jar;libraries\\java\\software/bernie/geckolib/fGeckolib-1.21.1-fabric-4.1.0.jar", "-DFabricMcEmu= net.minecraft.client.main.Main ", "net.digitalingot.rustextension.ProxiedStart", "net.fabricmc.loader.launch.knot.KnotClient", "--username", "TheMethodUE", "--version", "1.21.1-feather", "--gameDir", "C:\\Users\\<USER>\\AppData\\Roaming/.minecraft", "--assetsDir", "assets", "--assetIndex", "1.21.1-feather-game", "--uuid", "325c01cfe2704d89903573229de9c097", "--accessToken", "<hidden>", "--userType", "msa", "--versionType", "feather"]
info: Starting cleanup
info: [00:12:25] [main/INFO]: Loading Minecraft 1.21.1 with Fabric Loader 0.16.14
info: [00:12:25] [ForkJoinPool-1-worker-12/WARN]: Mod feather uses the version release/9d47e013 which isn't compatible with Loader's extended semantic version format (Could not parse version number component 'release/9d47e013'!), SemVer is recommended for reliably evaluating dependencies and prioritizing newer version
info: [00:12:26] [main/WARN]: Warnings were found!
 - Mod 'Sodium Extra' (sodium-extra) 0.6.0+mc1.21.1 recommends version 1.4.2 or later of reeses-sodium-options, which is missing!
	 - You should install version 1.4.2 or later of reeses-sodium-options for the optimal experience.
info: [00:12:26] [main/INFO]: Loading 102 mods:
	- advancedchatcore 1.21.1-1.5.12
	   |-- com_electronwill_night-config_core 3.6.5
	   |-- com_electronwill_night-config_toml 3.6.5
	   |-- com_github_darkkronicle_konstruct_addons 2.0.3-build1
	   |-- com_github_darkkronicle_konstruct_core 2.0.3-build1
	   |-- io_github_maowimpl_owo 2.0.0
	   |-- org_apache_commons_commons-csv 1.8
	   \-- org_mariuszgromada_math_mathparser_org-mxparser 4.4.2
	- advancedchathud 1.21.1-1.3.10
	- appleskin 3.0.6+mc1.21
	- citresewn 1.2.2*****
	   \-- citresewn-defaults 1.2.2*****
	- cloth-config 15.0.140
	   \-- cloth-basic-math 0.6.1
	- entity_texture_features 6.2.9
	   \-- org_apache_httpcomponents_httpmime 4.5.10
	- entityculling 1.7.4
	- fabric-api 0.115.1*****.1
	   |-- fabric-api-base 0.4.42+6573ed8c19
	   |-- fabric-api-lookup-api-v1 1.6.70+b559734419
	   |-- fabric-biome-api-v1 13.0.31+d527f9fd19
	   |-- fabric-block-api-v1 1.0.22+0af3f5a719
	   |-- fabric-block-view-api-v2 1.0.10+6573ed8c19
	   |-- fabric-blockrenderlayer-v1 1.1.52+0af3f5a719
	   |-- fabric-client-tags-api-v1 1.1.15+6573ed8c19
	   |-- fabric-command-api-v1 1.2.49+f71b366f19
	   |-- fabric-command-api-v2 2.2.28+6ced4dd919
	   |-- fabric-commands-v0 0.2.66+df3654b319
	   |-- fabric-content-registries-v0 8.0.18+b559734419
	   |-- fabric-convention-tags-v1 2.1.3+7f945d5b19
	   |-- fabric-convention-tags-v2 2.10.0+9465b64419
	   |-- fabric-crash-report-info-v1 0.2.29+0af3f5a719
	   |-- fabric-data-attachment-api-v1 1.4.1+9ed317f519
	   |-- fabric-data-generation-api-v1 20.2.26+16c4ae2519
	   |-- fabric-dimensions-v1 4.0.0+6fc22b9919
	   |-- fabric-entity-events-v1 1.7.0+2122d82819
	   |-- fabric-events-interaction-v0 0.7.13+ba9dae0619
	   |-- fabric-game-rule-api-v1 1.0.53+6ced4dd919
	   |-- fabric-item-api-v1 11.1.1+d5debaed19
	   |-- fabric-item-group-api-v1 4.1.6+6823f7cd19
	   |-- fabric-key-binding-api-v1 1.0.47+0af3f5a719
	   |-- fabric-keybindings-v0 0.2.45+df3654b319
	   |-- fabric-lifecycle-events-v1 2.5.0+01d9a51c19
	   |-- fabric-loot-api-v2 3.0.15+3f89f5a519
	   |-- fabric-loot-api-v3 1.0.3+3f89f5a519
	   |-- fabric-message-api-v1 6.0.13+6573ed8c19
	   |-- fabric-model-loading-api-v1 2.0.0+fe474d6b19
	   |-- fabric-networking-api-v1 4.3.0+c7469b2119
	   |-- fabric-object-builder-api-v1 15.2.1+40875a9319
	   |-- fabric-particles-v1 4.0.2+6573ed8c19
	   |-- fabric-recipe-api-v1 5.0.14+248df81c19
	   |-- fabric-registry-sync-v0 5.2.0+34f5d91419
	   |-- fabric-renderer-api-v1 3.4.0+c705a49c19
	   |-- fabric-renderer-indigo 1.7.0+c705a49c19
	   |-- fabric-renderer-registries-v1 3.2.68+df3654b319
	   |-- fabric-rendering-data-attachment-v1 0.3.48+73761d2e19
	   |-- fabric-rendering-fluids-v1 3.1.6+1daea21519
	   |-- fabric-rendering-v0 1.1.71+df3654b319
	   |-- fabric-rendering-v1 5.0.5+df16efd019
	   |-- fabric-resource-conditions-api-v1 4.3.0+8dc279b119
	   |-- fabric-resource-loader-v0 1.3.1+5b5275af19
	   |-- fabric-screen-api-v1 2.0.25+8b68f1c719
	   |-- fabric-screen-handler-api-v1 1.3.88+b559734419
	   |-- fabric-sound-api-v1 1.0.23+6573ed8c19
info: |-- fabric-transfer-api-v1 5.4.2+c24bd99419
	   \-- fabric-transitive-access-wideners-v1 6.2.0+45b9699719
	- fabric-language-kotlin 1.13.3+kotlin.2.1.21
	   |-- org_jetbrains_kotlin_kotlin-reflect 2.1.21
	   |-- org_jetbrains_kotlin_kotlin-stdlib 2.1.21
	   |-- org_jetbrains_kotlin_kotlin-stdlib-jdk7 2.1.21
	   |-- org_jetbrains_kotlin_kotlin-stdlib-jdk8 2.1.21
	   |-- org_jetbrains_kotlinx_atomicfu-jvm 0.27.0
	   |-- org_jetbrains_kotlinx_kotlinx-coroutines-core-jvm 1.10.2
	   |-- org_jetbrains_kotlinx_kotlinx-coroutines-jdk8 1.10.2
	   |-- org_jetbrains_kotlinx_kotlinx-datetime-jvm 0.6.2
	   |-- org_jetbrains_kotlinx_kotlinx-io-bytestring-jvm 0.7.0
	   |-- org_jetbrains_kotlinx_kotlinx-io-core-jvm 0.7.0
	   |-- org_jetbrains_kotlinx_kotlinx-serialization-cbor-jvm 1.8.1
	   |-- org_jetbrains_kotlinx_kotlinx-serialization-core-jvm 1.8.1
	   \-- org_jetbrains_kotlinx_kotlinx-serialization-json-jvm 1.8.1
	- fabricloader 0.16.14
	   \-- mixinextras 0.4.1
	- feather release/9d47e013
	- ferritecore 7.0.2-hotfix
	- fix 1.0
	- immediatelyfast 1.6.5*****.1
	   \-- net_lenni0451_reflect 1.3.4
	- itemscroller 0.24.58
	- java 21
	- lithium 0.15.0+mc1.21.1
	- malilib 0.21.8
	- minecraft 1.21.1
	- modernfix 5.23.0+mc1.21.1
	- modmenu 11.0.3
	- noxesium 2.3.3+d22848f
	   |-- com_noxcrew_noxesium_api 2.3.3+d22848f
	   |-- mixinextras 0.4.1
	   \-- org_khelekore_prtree 1.5
	- placeholder-api 2.4.2*****
	- sodium 0.6.13+mc1.21.1
	- sodium-extra 0.6.0+mc1.21.1
	- ukulib 1.3.0*****
	   \-- com_moandjiezana_toml_toml4j 0.7.2
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":188145072,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":188145072,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":188145072,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":188145072,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":192185280,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:12:31] [main/INFO]: SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/C:/Users/<USER>/AppData/Roaming/.minecraft/libraries/java/net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
info: [00:12:31] [main/INFO]: Compatibility level set to JAVA_21
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":69081592,"cpuUsage":0.0066}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:12:31] [main/INFO]: [Feather::Optifine]: Optifine is not detected
info: [00:12:31] [main/INFO]: [Feather::EssentialMod] Installed: false
info: [00:12:31] [main/WARN]: Mod 'ferritecore' attempted to override option 'mixin.alloc.blockstate', which doesn't exist, ignoring
info: [00:12:31] [main/INFO]: Option 'mixin.entity.collisions.fluid' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.entity.collisions.fluid=false'.
info: [00:12:31] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_support' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_support=false'.
[00:12:31] [main/INFO]: Option 'mixin.experimental.entity.block_caching.fluid_pushing' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.fluid_pushing=false'.
[00:12:31] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_touching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_touching=false'.
info: [00:12:31] [main/INFO]: Option 'mixin.experimental.entity.block_caching.suffocation' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.suffocation=false'.
info: [00:12:31] [main/INFO]: Option 'mixin.experimental.entity.block_caching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching=false'.
info: [00:12:31] [main/INFO]: Loaded configuration file for Lithium: 149 options available, 0 override(s) found
info: [00:12:31] [main/INFO]: Loaded configuration file for ModernFix 5.23.0+mc1.21.1: 61 options available, 0 override(s) found
info: [00:12:31] [main/INFO]: Applying Nashorn fix
info: [00:12:31] [main/INFO]: Loaded configuration file for Sodium: 43 options available, 0 override(s) found
info: [00:12:31] [main/INFO]: Loaded configuration file for Sodium Extra: 36 options available, 0 override(s) found
info: [00:12:31] [main/WARN]: Error loading class: mezz/jei/fabric/platform/RenderHelper (java.lang.ClassNotFoundException: mezz/jei/fabric/platform/RenderHelper)
info: [00:12:31] [main/WARN]: @Mixin target mezz.jei.fabric.platform.RenderHelper was not found appleskin.jei.mixins.json:JEIRenderHelperMixin from mod appleskin
info: [00:12:32] [main/WARN]: Error loading class: net/irisshaders/batchedentityrendering/impl/FullyBufferedMultiBufferSource (java.lang.ClassNotFoundException: net/irisshaders/batchedentityrendering/impl/FullyBufferedMultiBufferSource)
info: [00:12:32] [main/WARN]: Error loading class: net/irisshaders/iris/layer/InnerWrappedRenderType (java.lang.ClassNotFoundException: net/irisshaders/iris/layer/InnerWrappedRenderType)
info: [00:12:32] [main/WARN]: Error loading class: net/irisshaders/iris/layer/OuterWrappedRenderType (java.lang.ClassNotFoundException: net/irisshaders/iris/layer/OuterWrappedRenderType)
info: [00:12:32] [main/WARN]: Error loading class: dev/tr7zw/skinlayers/render/CustomizableModelPart (java.lang.ClassNotFoundException: dev/tr7zw/skinlayers/render/CustomizableModelPart)
info: [00:12:32] [main/WARN]: Error loading class: me/jellysquid/mods/sodium/client/render/immediate/model/EntityRenderer (java.lang.ClassNotFoundException: me/jellysquid/mods/sodium/client/render/immediate/model/EntityRenderer)
info: [00:12:32] [SentryExecutorServiceThreadFactory-0/WARN]: Error loading class: net/optifine/Config (java.lang.ClassNotFoundException: net/optifine/Config)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":183132312,"cpuUsage":0.0009}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:12:47] [ForkJoinPool-2-worker-10/WARN]: Mod feather uses the version release/9d47e013 which isn't compatible with Loader's extended semantic version format (Could not parse version number component 'release/9d47e013'!), SemVer is recommended for reliably evaluating dependencies and prioritizing newer version
info: [00:12:50] [main/INFO]: Searching for graphics cards...
info: [00:12:50] [main/INFO]: Found graphics adapter: AdapterInfo{vendor=NVIDIA, description='NVIDIA GeForce RTX 3080', adapterType=0x0000031B, openglIcdFilePath='C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_9425e4c3b1ac1c47\nvoglv64.dll', openglIcdVersion=32.0.15.6636}
info: [00:12:50] [main/WARN]: Sodium has applied one or more workarounds to prevent crashes or other issues on your system: [NVIDIA_THREADED_OPTIMIZATIONS_BROKEN]
[00:12:50] [main/WARN]: This is not necessarily an issue, but it may result in certain features or optimizations being disabled. You can sometimes fix these issues by upgrading your graphics driver.
info: [00:12:50] [main/INFO]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":322542232,"cpuUsage":0.0348}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:12:52] [Datafixer Bootstrap/INFO]: 226 Datafixer optimizations took 320 milliseconds
info: [00:12:53] [main/WARN]: Method overwrite conflict for method_21740 in modernfix-common.mixins.json:perf.remove_biome_temperature_cache.BiomeMixin from mod modernfix, previously written by net.caffeinemc.mods.lithium.mixin.world.temperature_cache.BiomeMixin. Skipping method.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":441556352,"cpuUsage":0.0327}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:12:55] [main/INFO]: Vanilla bootstrap took 3364 milliseconds
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":178351728,"cpuUsage":0.0266}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:12:56] [Render thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
info: [00:12:56] [Render thread/INFO]: Setting user: TheMethodUE
info: [00:12:57] [Render thread/INFO]: [citresewn] Registering CIT Conditions
info: [00:12:57] [Render thread/INFO]: [citresewn] Registering CIT Types
info: [00:12:57] [Render thread/INFO]: Loading Entity Texture Features, OptiFine's weirder younger half-brother that runs around making train noises.
info: [00:12:57] [Render thread/INFO]: [Indigo] Different rendering plugin detected; not applying Indigo.
info: [00:12:57] [Render thread/INFO]: Checking mod updates...
info: [00:12:57] [Worker-Main-1/INFO]: Update available for 'fabric-api@0.115.1*****.1', (-> 0.116.0*****.1)
info: [00:12:57] [Worker-Main-1/INFO]: Update available for 'ukulib@1.3.0*****', (-> 1.4.1*****)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":290643000,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":396543680,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":397399232,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":399109744,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":399965000,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:22] [Render thread/INFO]: Backend library: LWJGL version 3.3.3-snapshot
info: [00:13:22] [Render thread/INFO]: Modifying process environment to apply workarounds for the NVIDIA graphics driver...
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":192601528,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:23] [Render thread/INFO]: OpenGL Vendor: NVIDIA Corporation
[00:13:23] [Render thread/INFO]: OpenGL Renderer: NVIDIA GeForce RTX 3080/PCIe/SSE2
[00:13:23] [Render thread/INFO]: OpenGL Version: 3.2.0 NVIDIA 566.36
info: [00:13:23] [Render thread/INFO]: Modifying OpenGL context to apply workarounds for the NVIDIA graphics driver...
[00:13:23] [Render thread/INFO]: Enabling GL_DEBUG_OUTPUT_SYNCHRONOUS to force the NVIDIA driver to disable threadedcommand submission
info: [00:13:23] [Render thread/INFO]: Initializing ImmediatelyFast 1.6.5*****.1 on NVIDIA GeForce RTX 3080/PCIe/SSE2 (NVIDIA Corporation) with OpenGL 3.2.0 NVIDIA 566.36
info: [00:13:23] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\2025complex.jar', ignoring
info: [00:13:23] [Render thread/WARN]: Missing metadata in pack file/2025complex.zip
info: [00:13:23] [Render thread/WARN]: Missing metadata in pack file/cerv-mb.zip
info: [00:13:23] [Render thread/WARN]: Missing metadata in pack file/complex-modified.zip
info: [00:13:23] [Render thread/WARN]: Missing metadata in pack file/complex.zip
info: [00:13:23] [Render thread/WARN]: Missing metadata in pack file/complex8.4.24.zip
info: [00:13:23] [Render thread/WARN]: Missing metadata in pack file/complexdec2024.zip
info: [00:13:24] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font', ignoring
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":337364488,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:24] [Render thread/WARN]: Missing metadata in pack file/iheart-from-cerv.zip
info: [00:13:24] [Render thread/WARN]: Missing metadata in pack file/LarkSaber Purple.zip
info: [00:13:24] [Render thread/WARN]: Missing metadata in pack file/LarkSaber.zip
info: [00:13:24] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\TESTRBG', ignoring
info: [00:13:24] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: [00:13:24] [Render thread/INFO]: Removed resource pack file/COMPLEXBEE.zip from incompatibility list because it's now compatible
info: [00:13:24] [Render thread/WARN]: Removed resource pack appleskin from options because it is no longer compatible
[00:13:24] [Render thread/WARN]: Removed resource pack feather from options because it is no longer compatible
[00:13:24] [Render thread/WARN]: Removed resource pack modernfix from options because it is no longer compatible
info: [00:13:24] [Render thread/INFO]: Block minecraft:lily_pad had its color provider replaced with net.digitalingot.feather.jX$$Lambda/0x0000020336d56698@72d8d922 and will not use per-vertex coloring
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":545769080,"cpuUsage":0.0075}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":425303688,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:26] [Render thread/INFO]: Reloading ResourceManager: fabric, advancedchatcore, advancedchathud, appleskin, citresewn, citresewn-defaults, cloth-config, entity_texture_features, entityculling, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-blockrenderlayer-v1, fabric-client-tags-api-v1, fabric-command-api-v1, fabric-command-api-v2, fabric-commands-v0, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-keybindings-v0, fabric-language-kotlin, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-renderer-registries-v1, fabric-rendering-data-attachment-v1, fabric-rendering-fluids-v1, fabric-rendering-v0, fabric-rendering-v1, fabric-rendering-v3, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader, feather, immediatelyfast, itemscroller, lithium, malilib, modernfix, modmenu, noxesium, placeholder-api, sodium, sodium-extra, ukulib, vanilla, file/VzqsPurple.zip, file/SmallSwords, file/LarkSaber
info: [00:13:26] [Worker-ResourceReload-12/INFO]: Found unifont_jp_patch-15.1.05.hex, loading
[00:13:26] [Worker-ResourceReload-3/INFO]: Found unifont_all_no_pua-15.1.05.hex, loading
info: [00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/bottomTallSeaGrassAnimation.aseprite, ignoring
info: [00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/fireAnimation.aseprite, ignoring
info: [00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/lavaAnimation.ase, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/lavaFlowAnimation.aseprite, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/netherPortalAnimation.aseprite, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/netherPortalAnimation.gif, ignoring
info: [00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/oldLavaAnimation.ase, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/oldWaterFlowAnimation.aseprite, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/seaGrassAnimation.aseprite, ignoring
info: [00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/stonePallete.aseprite, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/tallSeaGrassAnimation.aseprite, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/topTallSeaGrassAnimation.aseprite, ignoring
[00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:textures/block/waterFlowAnimation.aseprite, ignoring
info: [00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.png, ignoring
info: [00:13:26] [Worker-ResourceReload-14/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.properties, ignoring
info: [00:13:26] [Render thread/INFO]: Loaded 0 words to profanity filter.
info: [00:13:26] [Worker-ResourceReload-6/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.png, ignoring
[00:13:26] [Worker-ResourceReload-6/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.properties, ignoring
info: [00:13:26] [Worker-ResourceReload-6/ERROR]: [citresewn] Using legacy nbt.display.Lore @L4 in minecraft:optifine/cit/lark_sword.properties from file/LarkSaber
info: [00:13:26] [Worker-ResourceReload-6/ERROR]: [citresewn] Using legacy nbt.display.Lore @L2 in minecraft:optifine/cit/misc/ancient_ingot/ancient_ingot.properties from file/VzqsPurple.zip
[00:13:26] [Worker-ResourceReload-6/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L2 in minecraft:optifine/cit/misc/firestone/firestone.properties from file/VzqsPurple.zip
info: [00:13:26] [Worker-ResourceReload-6/ERROR]: [citresewn] Using legacy nbt.display.Name @L2 in minecraft:optifine/cit/misc/firestone/firestone_bottom.properties from file/VzqsPurple.zip
[00:13:26] [Worker-ResourceReload-6/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L3 in minecraft:optifine/cit/misc/firestone/firestone_bottom.properties from file/VzqsPurple.zip
[00:13:26] [Worker-ResourceReload-6/ERROR]: [citresewn] Using legacy nbt.display.Name @L2 in minecraft:optifine/cit/misc/firestone/firestone_left.properties from file/VzqsPurple.zip
[00:13:26] [Worker-ResourceReload-6/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L3 in minecraft:optifine/cit/misc/firestone/firestone_left.properties from file/VzqsPurple.zip
[00:13:26] [Worker-ResourceReload-6/ERROR]: [citresewn] Using legacy nbt.display.Name @L2 in minecraft:optifine/cit/misc/firestone/firestone_top.properties from file/VzqsPurple.zip
[00:13:26] [Worker-ResourceReload-6/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L3 in minecraft:optifine/cit/misc/firestone/firestone_top.properties from file/VzqsPurple.zip
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":304458552,"cpuUsage":0.2703}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:27] [Worker-ResourceReload-6/INFO]: [citresewn] Loading item CIT models...
info: [00:13:27] [Worker-ResourceReload-6/WARN]: Unable to load model: 'minecraft:item/lightsaber/red/red_sword' referenced from: minecraft:item/netherite_sword: java.io.FileNotFoundException: minecraft:models/item/lightsaber/red/red_sword.json
info: [00:13:27] [Worker-ResourceReload-6/WARN]: Unable to load model: 'minecraft:item/hell/hellwrath_blade' referenced from: minecraft:item/netherite_sword: java.io.FileNotFoundException: minecraft:models/item/hell/hellwrath_blade.json
[00:13:27] [Worker-ResourceReload-6/WARN]: Unable to load model: 'minecraft:item/pirates/pirate_sword' referenced from: minecraft:item/netherite_sword: java.io.FileNotFoundException: minecraft:models/item/pirates/pirate_sword.json
[00:13:27] [Worker-ResourceReload-6/WARN]: Unable to load model: 'minecraft:item/lightsaber/blue/blue_sword' referenced from: minecraft:item/netherite_sword: java.io.FileNotFoundException: minecraft:models/item/lightsaber/blue/blue_sword.json
[00:13:27] [Worker-ResourceReload-6/WARN]: Unable to load model: 'minecraft:item/japan/samurai_sword' referenced from: minecraft:item/netherite_sword: java.io.FileNotFoundException: minecraft:models/item/japan/samurai_sword.json
info: [00:13:27] [Worker-ResourceReload-6/INFO]: [citresewn] Linking baked models to item CITs...
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":380840608,"cpuUsage":0.0518}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:28] [nioEventLoopGroup-3-1/INFO]: Initialized rust channel communication. Communicating on port 61362.
info: [00:13:28] [Render thread/WARN]: [Feather::IrisMod]: Iris is not detected
info: [00:13:28] [Render thread/INFO]: [GeckoLib] Initializing...
info: [00:13:28] [Render thread/INFO]: Started Feather (9d47e013)
info: [00:13:29] [Render thread/INFO]: OpenAL initialized on device OpenAL Soft on Voicemeeter Input (VB-Audio Voicemeeter VAIO)
info: [00:13:29] [Render thread/INFO]: Sound engine started
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":570590256,"cpuUsage":0.0257}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:29] [Render thread/INFO]: Created: 4096x4096x0 minecraft:textures/atlas/blocks.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 256x256x0 minecraft:textures/atlas/signs.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 512x512x0 minecraft:textures/atlas/banner_patterns.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 512x512x0 minecraft:textures/atlas/shield_patterns.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 1024x1024x0 minecraft:textures/atlas/armor_trims.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 256x256x0 minecraft:textures/atlas/chest.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 128x64x0 minecraft:textures/atlas/decorated_pot.png-atlas
[00:13:29] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/beds.png-atlas
[00:13:29] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/shulker_boxes.png-atlas
info: [00:13:29] [feather-scheduler-0/INFO]: Setting core backend token
info: [00:13:29] [feather-scheduler-0/INFO]: Authenticated with core backend
info: [00:13:29] [Render thread/INFO]: Created: 1024x512x0 minecraft:textures/atlas/particles.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/paintings.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 256x128x0 minecraft:textures/atlas/mob_effects.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 64x64x0 minecraft:textures/atlas/map_decorations.png-atlas
info: [00:13:29] [Render thread/INFO]: Created: 1024x512x0 minecraft:textures/atlas/gui.png-atlas
info: [00:13:29] [Render thread/WARN]: Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
info: [00:13:29] [Render thread/INFO]: [ETF]: reloading ETF data.
info: [00:13:29] [Render thread/INFO]: [ETF]: emissive suffixes loaded: {_e}.
info: [00:13:29] [Render thread/INFO]: [ETF]: emissive suffixes loaded: {_e}.
info: [00:13:29] [feather-scheduler-0/ERROR]: public net.digitalingot.feather.ve net.digitalingot.feather.fY$jy.aGF translated using FieldNamingPolicy.IDENTITY
info: [00:13:29] [nioEventLoopGroup-4-1/INFO]: Established Redstone connection to redstone.feathermc.com/5.161.61.153:1668
info: [00:13:29] [nioEventLoopGroup-4-1/INFO]: Authenticating with Redstone...
info: [00:13:30] [nioEventLoopGroup-4-1/INFO]: Authenticated with Redstone
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":463649664,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:30] [nioEventLoopGroup-2-1/INFO]: Connected to chat server
info: [00:13:30] [feather-scheduler-3/INFO]: Passed DRM check
info: [00:13:31] [Render thread/WARN]: Game took 66.616 seconds to start
info: [00:13:38] [Render thread/ERROR]: Can't ping DuckyMama.feathermc.gg: Disconnected
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":816842752,"cpuUsage":0.049}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:39] [Render thread/ERROR]: Can't ping IWannaEat.feathermc.gg: Disconnected
info: [00:13:39] [Render thread/ERROR]: Can't ping iwannaeat.feathermc.gg: Disconnected
info: [00:13:39] [Render thread/ERROR]: Can't ping Glitchyz.feathermc.gg: Disconnected
info: [00:13:40] [Render thread/ERROR]: Can't ping cerv.feathermc.gg: Disconnected
info: [00:13:40] [Render thread/ERROR]: Can't ping d05ed1dec7844184901069cee3463c44.feathermc.gg: Disconnected
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":354072224,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:41] [Render thread/INFO]: Connecting to 5XNJK2OC7215VW3tHhFlz0dLInpYqqFa.na-lax.liquidproxy.net, 25565
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":483541640,"cpuUsage":0.0458}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:49] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: [00:13:49] [Render thread/INFO]: Started 10 worker threads
info: [00:13:49] [Render thread/INFO]: Detected ImmediatelyFast! Disabling HUD Batching incompatibility...
info: [00:13:49] [Render thread/INFO]: Loaded 5 advancements
info: [00:13:50] [Render thread/INFO]: [STDOUT]: Lithium Cached BlockState Flags are disabled!
info: [00:13:50] [Render thread/INFO]: Stopping worker threads
info: [00:13:50] [Render thread/INFO]: Started 10 worker threads
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":795542424,"cpuUsage":0.0278}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":378492864,"cpuUsage":0.0544}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":496815968,"cpuUsage":0.0284}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":620635600,"cpuUsage":0.0314}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":731006968,"cpuUsage":0.0323}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:56] [Render thread/INFO]: [CHAT] ✔ Sending you to Lifesteal...
info: [00:13:56] [Render thread/INFO]: Stopping worker threads
info: [00:13:56] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":456652032,"cpuUsage":0.059}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:56] [Render thread/WARN]: Not all defined tags for registry ResourceKey[minecraft:root / minecraft:item] are present in data pack: minecraft:music_discs
info: [00:13:57] [Render thread/INFO]: Started 10 worker threads
info: [00:13:57] [Render thread/INFO]: Stopping worker threads
info: [00:13:57] [Render thread/INFO]: Started 10 worker threads
info: [00:13:57] [Render thread/INFO]: [CHAT] ! Don't forget to vote to be eligible to receive rewards from our vote party.
info: [00:13:57] [Render thread/WARN]: Time from main menu to in-game was 16.206095 seconds
info: [00:13:57] [Render thread/WARN]: Total time to load game and open world was 82.82209 seconds
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":684101752,"cpuUsage":0.0476}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:58] [Render thread/INFO]: [CHAT]
info: [00:13:58] [Render thread/INFO]: [CHAT]  COMPLEX LIFESTEAL 1.21
info: [00:13:58] [Render thread/INFO]: [CHAT]    ▪ Website: mc-complex.com
info: [00:13:58] [Render thread/INFO]: [CHAT]    ▪ Store: store.mc-complex.com
info: [00:13:58] [Render thread/INFO]: [CHAT]    ▪ Discord: discord.gg/ComplexVanilla
info: [00:13:58] [Render thread/INFO]: [CHAT]    ▪ Rules: bit.ly/ComplexVanillaRules
info: [00:13:58] [Render thread/INFO]: [CHAT]
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":434643376,"cpuUsage":0.1037}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:13:59] [Render thread/INFO]: [CHAT]
info: [00:13:59] [Render thread/INFO]: [CHAT] (/clan) ‹ᴋɪᴛᴛᴇɴꜱ› Meow :3
info: [00:13:59] [Render thread/INFO]: [CHAT]
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":693847944,"cpuUsage":0.0294}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:00] [Render thread/INFO]: [CHAT] ! You have some items pending in /collect!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":426195968,"cpuUsage":0.0179}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:01] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\2025complex.jar', ignoring
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/2025complex.zip
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/cerv-mb.zip
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/complex-modified.zip
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/complex.zip
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/complex8.4.24.zip
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/complexdec2024.zip
info: [00:14:01] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font', ignoring
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/iheart-from-cerv.zip
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/LarkSaber Purple.zip
info: [00:14:01] [Render thread/WARN]: Missing metadata in pack file/LarkSaber.zip
info: [00:14:01] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\TESTRBG', ignoring
info: [00:14:01] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":761277072,"cpuUsage":0.0447}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:02] [Render thread/INFO]: [CHAT] (/freerunes) ⭐ You have 3 Free Runes available!
info: [00:14:02] [Render thread/INFO]: [CHAT] (/dailies) ⭐ You have 6 Daily Rewards available!
info: [00:14:02] [Render thread/INFO]: [CHAT] (/verify)  Verify your Discord to receive a free reward!
info: [00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: [00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: [00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: [00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:14:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":466255368,"cpuUsage":0.0582}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":778703192,"cpuUsage":0.0325}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1093908368,"cpuUsage":0.0315}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":621096576,"cpuUsage":0.0391}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:06] [Render thread/INFO]: [CHAT] あ [Member] qjo_Cosmic_aqn: why i am on three hearts
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":951152824,"cpuUsage":0.0362}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":492123912,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":827725072,"cpuUsage":0.0266}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":377417688,"cpuUsage":0.0372}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:11] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\2025complex.jar', ignoring
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/2025complex.zip
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/cerv-mb.zip
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/complex-modified.zip
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/complex.zip
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/complex8.4.24.zip
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/complexdec2024.zip
info: [00:14:11] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font', ignoring
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/iheart-from-cerv.zip
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/LarkSaber Purple.zip
info: [00:14:11] [Render thread/WARN]: Missing metadata in pack file/LarkSaber.zip
info: [00:14:11] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\TESTRBG', ignoring
info: [00:14:11] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: [00:14:11] [Render thread/INFO]: Reloading ResourceManager: vanilla, fabric, advancedchatcore, advancedchathud, appleskin, citresewn, citresewn-defaults, cloth-config, entity_texture_features, entityculling, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-blockrenderlayer-v1, fabric-client-tags-api-v1, fabric-command-api-v1, fabric-command-api-v2, fabric-commands-v0, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-keybindings-v0, fabric-language-kotlin, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-renderer-registries-v1, fabric-rendering-data-attachment-v1, fabric-rendering-fluids-v1, fabric-rendering-v0, fabric-rendering-v1, fabric-rendering-v3, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader, feather, immediatelyfast, itemscroller, lithium, malilib, modernfix, modmenu, noxesium, placeholder-api, sodium, sodium-extra, ukulib, file/VzqsPurple.zip, file/COMPLEXBEE.zip, file/SmallSwords, file/LarkSaber
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":727564528,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:11] [Worker-ResourceReload-5/INFO]: Found unifont_all_no_pua-15.1.05.hex, loading
info: [00:14:11] [Worker-ResourceReload-5/INFO]: Found unifont_jp_patch-15.1.05.hex, loading
info: [00:14:11] [Worker-ResourceReload-7/WARN]: Codepoint 'a45d' declared multiple times in minecraft:textures/complex/ui/uis.png
info: [00:14:11] [Worker-ResourceReload-7/WARN]: Codepoint 'a45d' declared multiple times in minecraft:textures/complex/ui/uis.png
info: [00:14:11] [Worker-ResourceReload-7/WARN]: Codepoint 'a45d' declared multiple times in minecraft:textures/font/leaderboard/icons.png
info: [00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/bottomTallSeaGrassAnimation.aseprite, ignoring
info: [00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/fireAnimation.aseprite, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/lavaAnimation.ase, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/lavaFlowAnimation.aseprite, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/netherPortalAnimation.aseprite, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/netherPortalAnimation.gif, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/oldLavaAnimation.ase, ignoring
info: [00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/oldWaterFlowAnimation.aseprite, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/seaGrassAnimation.aseprite, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/stonePallete.aseprite, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/tallSeaGrassAnimation.aseprite, ignoring
[00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/topTallSeaGrassAnimation.aseprite, ignoring
info: [00:14:11] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:textures/block/waterFlowAnimation.aseprite, ignoring
info: [00:14:12] [Worker-ResourceReload-12/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.png, ignoring
info: [00:14:12] [Worker-ResourceReload-12/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.properties, ignoring
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor1.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor10.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor11.properties from file/COMPLEXBEE.zip
[00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor12.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor13.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor14.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor15.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor16.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor17.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor18.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor19.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor2.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor20.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor21.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor22.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor23.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor24.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor25.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor26.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor27.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor28.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor3.properties from file/COMPLEXBEE.zip
[00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor4.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor5.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor6.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor7.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor8.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L7 in minecraft:optifine/cit/armor/armor9.properties from file/COMPLEXBEE.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: [citresewn] Using legacy nbt.display.Lore @L4 in minecraft:optifine/cit/lark_sword.properties from file/LarkSaber
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: [citresewn] Using legacy nbt.display.Lore @L2 in minecraft:optifine/cit/misc/ancient_ingot/ancient_ingot.properties from file/VzqsPurple.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L2 in minecraft:optifine/cit/misc/firestone/firestone.properties from file/VzqsPurple.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: [citresewn] Using legacy nbt.display.Name @L2 in minecraft:optifine/cit/misc/firestone/firestone_bottom.properties from file/VzqsPurple.zip
[00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L3 in minecraft:optifine/cit/misc/firestone/firestone_bottom.properties from file/VzqsPurple.zip
[00:14:12] [Worker-ResourceReload-12/ERROR]: [citresewn] Using legacy nbt.display.Name @L2 in minecraft:optifine/cit/misc/firestone/firestone_left.properties from file/VzqsPurple.zip
info: [00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L3 in minecraft:optifine/cit/misc/firestone/firestone_left.properties from file/VzqsPurple.zip
[00:14:12] [Worker-ResourceReload-12/ERROR]: [citresewn] Using legacy nbt.display.Name @L2 in minecraft:optifine/cit/misc/firestone/firestone_top.properties from file/VzqsPurple.zip
[00:14:12] [Worker-ResourceReload-12/ERROR]: {citresewn} Errored while parsing CIT: NBT condition is not supported since 1.21 @L3 in minecraft:optifine/cit/misc/firestone/firestone_top.properties from file/VzqsPurple.zip
info: [00:14:12] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.png, ignoring
info: [00:14:12] [Worker-ResourceReload-3/WARN]: Invalid path in datapack: minecraft:optifine/cit/kb sword/kb_sword_2.properties, ignoring
info: [00:14:12] [Worker-ResourceReload-12/INFO]: [citresewn] Loading item CIT models...
info: [00:14:12] [Worker-ResourceReload-12/WARN]: Unable to load model: 'minecraft:40' referenced from: minecraft:item/shield: java.io.FileNotFoundException: minecraft:models/40.json
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":500512056,"cpuUsage":0.2233}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:13] [Worker-ResourceReload-12/INFO]: [citresewn] Linking baked models to item CITs...
info: [00:14:13] [Worker-ResourceReload-12/WARN]: Missing textures in model minecraft:netherite_pickaxe#inventory:
    minecraft:textures/atlas/blocks.png:summerset:animations/bubble_1
info: [00:14:13] [Worker-ResourceReload-12/WARN]: Missing textures in model minecraft:paper#inventory:
    minecraft:textures/atlas/blocks.png:minecraft:item/emote/glowing_star
info: [00:14:13] [Render thread/WARN]: Received passengers for unknown entity
info: [00:14:13] [Render thread/INFO]: OpenAL initialized on device OpenAL Soft on Voicemeeter Input (VB-Audio Voicemeeter VAIO)
info: [00:14:13] [Render thread/INFO]: Sound engine started
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1473857648,"cpuUsage":0.1222}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:13] [Render thread/INFO]: Created: 8192x8192x0 minecraft:textures/atlas/blocks.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 256x256x0 minecraft:textures/atlas/signs.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 512x512x0 minecraft:textures/atlas/banner_patterns.png-atlas
[00:14:13] [Render thread/INFO]: Created: 512x512x0 minecraft:textures/atlas/shield_patterns.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 1024x1024x0 minecraft:textures/atlas/armor_trims.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 256x256x0 minecraft:textures/atlas/chest.png-atlas
[00:14:13] [Render thread/INFO]: Created: 128x64x0 minecraft:textures/atlas/decorated_pot.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/beds.png-atlas
[00:14:13] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/shulker_boxes.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 1024x512x0 minecraft:textures/atlas/particles.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 512x256x0 minecraft:textures/atlas/paintings.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 256x128x0 minecraft:textures/atlas/mob_effects.png-atlas
info: [00:14:13] [Render thread/INFO]: Created: 64x64x0 minecraft:textures/atlas/map_decorations.png-atlas
[00:14:13] [Render thread/INFO]: Created: 1024x512x0 minecraft:textures/atlas/gui.png-atlas
info: [00:14:13] [Render thread/WARN]: Shader position_color could not find uniform named GameTime in the specified shader program.
info: [00:14:13] [Render thread/WARN]: Shader rendertype_armor_cutout_no_cull could not find uniform named GameTime in the specified shader program.
info: [00:14:13] [Render thread/WARN]: Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
info: [00:14:13] [Render thread/WARN]: Shader rendertype_gui could not find uniform named GameTime in the specified shader program.
info: [00:14:13] [Render thread/WARN]: Resource pack file/COMPLEXBEE.zip is not compatible with font atlas resizing. Temporarily disabling font atlas resizing.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1507849904,"cpuUsage":0.0583}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:14] [Render thread/INFO]: Stopping worker threads
info: [00:14:14] [Render thread/INFO]: Started 10 worker threads
info: [00:14:14] [Render thread/INFO]: [ETF]: reloading ETF data.
info: [00:14:14] [Render thread/INFO]: [ETF]: emissive suffixes loaded: {_e}.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1826396800,"cpuUsage":0.0649}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":571820128,"cpuUsage":0.0429}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":832696936,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:18] [Render thread/INFO]: [Feather::Geckolib]: Loading model for spring2025BeeHat
info: [00:14:18] [Render thread/WARN]: [GeckoLib] Defaulting to render for spring2025BeeHat
info: [00:14:18] [Render thread/WARN]: [GeckoLib] Defaulting to render for spring2025BeeHat
[00:14:18] [Render thread/WARN]: [GeckoLib] Defaulting to render for spring2025BeeHat
info: [00:14:18] [Render thread/INFO]: [Feather::Geckolib]: Loading model for spring2025BeeAura
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1165372736,"cpuUsage":0.0209}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1577664512,"cpuUsage":0.019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1934097240,"cpuUsage":0.0305}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":776499768,"cpuUsage":0.0396}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1142251160,"cpuUsage":0.0362}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1529277424,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1911514680,"cpuUsage":0.0182}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":795518560,"cpuUsage":0.057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:26] [Worker-Main-12/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:14:26] [Worker-Main-6/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
info: [00:14:26] [Worker-Main-10/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:14:26] [Worker-Main-12/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:14:26] [Worker-Main-6/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
info: [00:14:26] [Worker-Main-12/ERROR]: Could not decode textures payload
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:136) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:14:26] [Worker-Main-10/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:14:26] [Worker-Main-6/ERROR]: Could not decode textures payload
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:136) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
info: [00:14:26] [Worker-Main-10/ERROR]: Could not decode textures payload
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:136) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
info: [00:14:26] [ForkJoinPool.commonPool-worker-2/ERROR]: [Feather::TierTagger]: Error getting player info (233b7113-d58a-36b2-b863-cae3a48a9bda); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/233b7113d58a36b2b863cae3a48a9bda
info: [00:14:26] [ForkJoinPool.commonPool-worker-1/ERROR]: [Feather::TierTagger]: Error getting player info (c62db10c-9f59-3c6a-8f6b-467a6048d18f); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/c62db10c9f593c6a8f6b467a6048d18f
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1184484000,"cpuUsage":0.0428}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:26] [Render thread/WARN]: Received passengers for unknown entity
info: [00:14:26] [Render thread/WARN]: Received passengers for unknown entity
info: [00:14:26] [Render thread/WARN]: Received passengers for unknown entity
info: [00:14:26] [Render thread/WARN]: Received passengers for unknown entity
info: [00:14:26] [ForkJoinPool.commonPool-worker-2/ERROR]: [Feather::TierTagger]: Error getting player info (ae481e95-fe21-3236-9649-2f4d2563a95c); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/ae481e95fe21323696492f4d2563a95c
info: [00:14:26] [ForkJoinPool.commonPool-worker-1/ERROR]: [Feather::TierTagger]: Error getting player info (b8bfff62-8ed6-3e9c-be69-69ab7af1d94a); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/b8bfff628ed63e9cbe6969ab7af1d94a
info: [00:14:26] [ForkJoinPool.commonPool-worker-4/ERROR]: [Feather::TierTagger]: Error getting player info (b3e450e2-04ff-3088-bac7-fa53a91c382c); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/b3e450e204ff3088bac7fa53a91c382c
[00:14:26] [ForkJoinPool.commonPool-worker-7/ERROR]: [Feather::TierTagger]: Error getting player info (d328d402-b8d9-3f24-b101-df0a00a89ef3); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/d328d402b8d93f24b101df0a00a89ef3
info: [00:14:26] [ForkJoinPool.commonPool-worker-9/ERROR]: [Feather::TierTagger]: Error getting player info (5a09dec8-791b-3a88-8d8f-6c83c3a5deae); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/5a09dec8791b3a888d8f6c83c3a5deae
[00:14:26] [ForkJoinPool.commonPool-worker-10/ERROR]: [Feather::TierTagger]: Error getting player info (c14116d4-5bc6-3b15-8b9d-02023a731f7c); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/c14116d45bc63b158b9d02023a731f7c
[00:14:26] [ForkJoinPool.commonPool-worker-5/ERROR]: [Feather::TierTagger]: Error getting player info (22a325e7-9cc6-3bfb-b2e9-31b42415e7e0); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/22a325e79cc63bfbb2e931b42415e7e0
info: [00:14:26] [ForkJoinPool.commonPool-worker-6/ERROR]: [Feather::TierTagger]: Error getting player info (272c7a34-4dae-3325-8160-818c110f2ea6); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/272c7a344dae33258160818c110f2ea6
info: [00:14:26] [ForkJoinPool.commonPool-worker-3/ERROR]: [Feather::TierTagger]: Error getting player info (46abfd36-9b36-3dbc-81ef-00c9bd7c5f70); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/46abfd369b363dbc81ef00c9bd7c5f70
info: [00:14:26] [ForkJoinPool.commonPool-worker-8/ERROR]: [Feather::TierTagger]: Error getting player info (3d7c3b0e-a862-385a-846d-057b9736ada9); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/3d7c3b0ea862385a846d057b9736ada9
info: [00:14:26] [ForkJoinPool.commonPool-worker-12/ERROR]: [Feather::TierTagger]: Error getting player info (8b385de1-a584-3ba5-b389-183006182254); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/8b385de1a5843ba5b389183006182254
info: [00:14:26] [ForkJoinPool.commonPool-worker-15/ERROR]: [Feather::TierTagger]: Error getting player info (43ee979c-7a96-3303-b776-48e44e618730); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/43ee979c7a963303b77648e44e618730
info: [00:14:26] [ForkJoinPool.commonPool-worker-14/ERROR]: [Feather::TierTagger]: Error getting player info (60226ca3-7c5c-350f-98a4-9fa796a91554); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/60226ca37c5c350f98a49fa796a91554
info: [00:14:26] [ForkJoinPool.commonPool-worker-13/ERROR]: [Feather::TierTagger]: Error getting player info (466e49e4-15ad-34e6-b603-3088e5b62343); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/466e49e415ad34e6b6033088e5b62343
info: [00:14:26] [ForkJoinPool.commonPool-worker-11/ERROR]: [Feather::TierTagger]: Error getting player info (66fb65ca-75b0-3995-b87b-2462bc239918); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/66fb65ca75b03995b87b2462bc239918
info: [00:14:27] [ForkJoinPool.commonPool-worker-1/ERROR]: [Feather::TierTagger]: Error getting player info (12295ae2-7c2c-3ad5-9027-2ec073a69821); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/12295ae27c2c3ad590272ec073a69821
[00:14:27] [ForkJoinPool.commonPool-worker-2/ERROR]: [Feather::TierTagger]: Error getting player info (1c5adf3e-e845-3542-9d10-6088b9308f73); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/1c5adf3ee84535429d106088b9308f73
info: [00:14:27] [ForkJoinPool.commonPool-worker-4/ERROR]: [Feather::TierTagger]: Error getting player info (1ab2d0b2-a40b-3d59-ba03-fcef671fbc66); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/1ab2d0b2a40b3d59ba03fcef671fbc66
info: [00:14:27] [ForkJoinPool.commonPool-worker-7/ERROR]: [Feather::TierTagger]: Error getting player info (87223cc0-c96d-3689-9716-c6d828c121dd); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/87223cc0c96d36899716c6d828c121dd
info: [00:14:27] [ForkJoinPool.commonPool-worker-9/ERROR]: [Feather::TierTagger]: Error getting player info (805c68df-4b15-3d93-a531-a2ec303a05a2); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/805c68df4b153d93a531a2ec303a05a2
info: [00:14:27] [ForkJoinPool.commonPool-worker-5/ERROR]: [Feather::TierTagger]: Error getting player info (aaebfc95-7a19-3dd6-bf77-4c65183ad0a0); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/aaebfc957a193dd6bf774c65183ad0a0
info: [00:14:27] [ForkJoinPool.commonPool-worker-10/ERROR]: [Feather::TierTagger]: Error getting player info (1e9eb712-48e0-3528-b65d-cfaaef1b96da); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/1e9eb71248e03528b65dcfaaef1b96da
info: [00:14:27] [ForkJoinPool.commonPool-worker-6/ERROR]: [Feather::TierTagger]: Error getting player info (3b8e2b62-98e0-3efe-9fb1-0052eb3ac8e3); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/3b8e2b6298e03efe9fb10052eb3ac8e3
info: [00:14:27] [ForkJoinPool.commonPool-worker-3/ERROR]: [Feather::TierTagger]: Error getting player info (77751f53-7c30-3430-a373-58d005ecbc8f); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/77751f537c303430a37358d005ecbc8f
info: [00:14:27] [ForkJoinPool.commonPool-worker-2/ERROR]: [Feather::TierTagger]: Error getting player info (1d5fb1ff-6d1d-325b-b525-bb445217c809); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/1d5fb1ff6d1d325bb525bb445217c809
info: [00:14:27] [ForkJoinPool.commonPool-worker-8/ERROR]: [Feather::TierTagger]: Error getting player info (b0ca7823-af84-3477-bb6a-486c21016b21); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/b0ca7823af843477bb6a486c21016b21
[00:14:27] [ForkJoinPool.commonPool-worker-12/ERROR]: [Feather::TierTagger]: Error getting player info (e729a7de-7bcb-3e5d-b7e8-72e629dd6725); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/e729a7de7bcb3e5db7e872e629dd6725
info: [00:14:27] [ForkJoinPool.commonPool-worker-5/ERROR]: [Feather::TierTagger]: Error getting player info (59732695-64c0-36b9-82a4-791f79334b86); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/5973269564c036b982a4791f79334b86
info: [00:14:27] [ForkJoinPool.commonPool-worker-15/ERROR]: [Feather::TierTagger]: Error getting player info (ae8f943b-3f2c-3d4b-b4ac-e6b6518fac54); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/ae8f943b3f2c3d4bb4ace6b6518fac54
info: [00:14:27] [ForkJoinPool.commonPool-worker-14/ERROR]: [Feather::TierTagger]: Error getting player info (9257302e-8e8e-32f9-a4dc-54339314e269); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/9257302e8e8e32f9a4dc54339314e269
info: [00:14:27] [ForkJoinPool.commonPool-worker-11/ERROR]: [Feather::TierTagger]: Error getting player info (77b733e6-084c-3e4d-9c6a-6c9e27397d23); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/77b733e6084c3e4d9c6a6c9e27397d23
info: [00:14:27] [ForkJoinPool.commonPool-worker-13/ERROR]: [Feather::TierTagger]: Error getting player info (3d036aba-a7b8-3563-9432-8d1b5bd30829); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/3d036abaa7b8356394328d1b5bd30829
info: [00:14:27] [ForkJoinPool.commonPool-worker-1/ERROR]: [Feather::TierTagger]: Error getting player info (537d2713-dc0c-3d98-aad4-1d9eca684efe); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/537d2713dc0c3d98aad41d9eca684efe
info: [00:14:27] [ForkJoinPool.commonPool-worker-11/ERROR]: [Feather::TierTagger]: Error getting player info (5028fa95-5fe7-3a39-989b-612a5ef41e85); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/5028fa955fe73a39989b612a5ef41e85
info: [00:14:27] [ForkJoinPool.commonPool-worker-7/ERROR]: [Feather::TierTagger]: Error getting player info (3abcc00d-c4e8-382b-9023-8f07b6126e84); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/3abcc00dc4e8382b90238f07b6126e84
info: [00:14:27] [ForkJoinPool.commonPool-worker-4/ERROR]: [Feather::TierTagger]: Error getting player info (dc5fab5c-159b-3bc9-a655-256c5b1bfc72); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/dc5fab5c159b3bc9a655256c5b1bfc72
info: [00:14:27] [ForkJoinPool.commonPool-worker-9/ERROR]: [Feather::TierTagger]: Error getting player info (32648c8b-f56f-38d9-b8d1-9f97aba33af2); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/32648c8bf56f38d9b8d19f97aba33af2
info: [00:14:27] [ForkJoinPool.commonPool-worker-6/ERROR]: [Feather::TierTagger]: Error getting player info (cd628d20-a035-3ca2-af69-62e020135920); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/cd628d20a0353ca2af6962e020135920
info: [00:14:27] [ForkJoinPool.commonPool-worker-10/ERROR]: [Feather::TierTagger]: Error getting player info (9f1f0f6d-0d3e-3f97-b3c2-78618a4a6649); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/9f1f0f6d0d3e3f97b3c278618a4a6649
info: [00:14:27] [ForkJoinPool.commonPool-worker-3/ERROR]: [Feather::TierTagger]: Error getting player info (e3e209d1-cb1e-3b9b-8181-8e7d084dfb64); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/e3e209d1cb1e3b9b81818e7d084dfb64
info: [00:14:27] [ForkJoinPool.commonPool-worker-8/ERROR]: [Feather::TierTagger]: Error getting player info (95c2669c-4d4b-3fd6-9261-d952b75d637e); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/95c2669c4d4b3fd69261d952b75d637e
info: [00:14:27] [ForkJoinPool.commonPool-worker-12/ERROR]: [Feather::TierTagger]: Error getting player info (2467bccd-174a-3abb-a4b0-7ce400192a18); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/2467bccd174a3abba4b07ce400192a18
info: [00:14:27] [ForkJoinPool.commonPool-worker-2/ERROR]: [Feather::TierTagger]: Error getting player info (44a5fcf2-4aba-300f-8393-e4f23576c488); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/44a5fcf24aba300f8393e4f23576c488
info: [00:14:27] [ForkJoinPool.commonPool-worker-5/ERROR]: [Feather::TierTagger]: Error getting player info (523bf512-a996-3a69-a107-7847067d44d0); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/523bf512a9963a69a1077847067d44d0
info: [00:14:27] [ForkJoinPool.commonPool-worker-15/ERROR]: [Feather::TierTagger]: Error getting player info (d75f52f9-79f7-33cb-b934-05d96f88abc8); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/d75f52f979f733cbb93405d96f88abc8
info: [00:14:27] [ForkJoinPool.commonPool-worker-14/ERROR]: [Feather::TierTagger]: Error getting player info (bea8b00e-c144-3710-929b-c6448af26a9d); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/bea8b00ec1443710929bc6448af26a9d
info: [00:14:27] [ForkJoinPool.commonPool-worker-13/ERROR]: [Feather::TierTagger]: Error getting player info (e62727c9-e56a-3bc6-b035-b58795d63375); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/e62727c9e56a3bc6b035b58795d63375
info: [00:14:27] [ForkJoinPool.commonPool-worker-12/ERROR]: [Feather::TierTagger]: Error getting player info (ebb60ea5-08bd-3428-b948-480d3023ee03); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/ebb60ea508bd3428b948480d3023ee03
info: [00:14:27] [ForkJoinPool.commonPool-worker-1/ERROR]: [Feather::TierTagger]: Error getting player info (69a21e72-0c29-3793-99fd-6745e28a392a); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/69a21e720c29379399fd6745e28a392a
info: [00:14:27] [ForkJoinPool.commonPool-worker-15/ERROR]: [Feather::TierTagger]: Error getting player info (2cf47388-dfdf-3612-b356-f9ee50ccd407); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/2cf47388dfdf3612b356f9ee50ccd407
info: [00:14:27] [ForkJoinPool.commonPool-worker-11/ERROR]: [Feather::TierTagger]: Error getting player info (7851fa5b-9e95-3358-b7dc-beaabf9fb493); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/7851fa5b9e953358b7dcbeaabf9fb493
info: [00:14:27] [ForkJoinPool.commonPool-worker-10/ERROR]: [Feather::TierTagger]: Error getting player info (47f80b5f-ee4d-3946-b136-8a90d2e719d9); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/47f80b5fee4d3946b1368a90d2e719d9
info: [00:14:27] [ForkJoinPool.commonPool-worker-7/ERROR]: [Feather::TierTagger]: Error getting player info (a6568fd7-11c9-3c8f-b9e9-e771fa2ce901); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/a6568fd711c93c8fb9e9e771fa2ce901
info: [00:14:27] [ForkJoinPool.commonPool-worker-9/ERROR]: [Feather::TierTagger]: Error getting player info (25df4f7e-9234-3b2d-a73d-5984fb9db9ab); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/25df4f7e92343b2da73d5984fb9db9ab
info: [00:14:27] [ForkJoinPool.commonPool-worker-4/ERROR]: [Feather::TierTagger]: Error getting player info (f8bbb1c3-fa0d-3d62-8cb5-293e867933a2); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/f8bbb1c3fa0d3d628cb5293e867933a2
[00:14:27] [ForkJoinPool.commonPool-worker-6/ERROR]: [Feather::TierTagger]: Error getting player info (6138cf72-e528-3e0a-b75c-a853671d103a); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/6138cf72e5283e0ab75ca853671d103a
info: [00:14:27] [ForkJoinPool.commonPool-worker-3/ERROR]: [Feather::TierTagger]: Error getting player info (60a0b7e3-21ed-33c8-bfa8-ad28a32c481b); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/60a0b7e321ed33c8bfa8ad28a32c481b
info: [00:14:27] [ForkJoinPool.commonPool-worker-15/ERROR]: [Feather::TierTagger]: Error getting player info (f02c53d0-8360-3176-9b78-a388b1821f45); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/f02c53d0836031769b78a388b1821f45
info: [00:14:27] [ForkJoinPool.commonPool-worker-8/ERROR]: [Feather::TierTagger]: Error getting player info (922ec1c6-3ea3-324e-a077-f0b239ec2721); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/922ec1c63ea3324ea077f0b239ec2721
info: [00:14:27] [ForkJoinPool.commonPool-worker-2/ERROR]: [Feather::TierTagger]: Error getting player info (f24a0415-7032-3e17-95a8-5494a597c200); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/f24a041570323e1795a85494a597c200
info: [00:14:27] [ForkJoinPool.commonPool-worker-10/ERROR]: [Feather::TierTagger]: Error getting player info (e18803e2-9a00-37d0-b1ee-41d60971c3c9); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/e18803e29a0037d0b1ee41d60971c3c9
info: [00:14:27] [ForkJoinPool.commonPool-worker-5/ERROR]: [Feather::TierTagger]: Error getting player info (4e8328e6-0b25-382b-a7fe-129ca0750612); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/4e8328e60b25382ba7fe129ca0750612
info: [00:14:27] [ForkJoinPool.commonPool-worker-3/ERROR]: [Feather::TierTagger]: Error getting player info (1f23d40e-8d24-3e11-949f-f74a1a48a6e0); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/1f23d40e8d243e11949ff74a1a48a6e0
info: [00:14:27] [ForkJoinPool.commonPool-worker-14/ERROR]: [Feather::TierTagger]: Error getting player info (60a846c0-3742-3892-a69a-e74d80a34d69); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/60a846c037423892a69ae74d80a34d69
info: [00:14:27] [ForkJoinPool.commonPool-worker-13/ERROR]: [Feather::TierTagger]: Error getting player info (b808029c-e54b-3b71-b5e9-b224f4185de0); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/b808029ce54b3b71b5e9b224f4185de0
info: [00:14:27] [ForkJoinPool.commonPool-worker-8/ERROR]: [Feather::TierTagger]: Error getting player info (ce49dbf2-109a-3505-8661-e382fa8e82ac); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/ce49dbf2109a35058661e382fa8e82ac
info: [00:14:27] [ForkJoinPool.commonPool-worker-12/ERROR]: [Feather::TierTagger]: Error getting player info (a8f51baf-eac5-343a-814d-400aad39372a); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/a8f51bafeac5343a814d400aad39372a
info: [00:14:27] [ForkJoinPool.commonPool-worker-2/ERROR]: [Feather::TierTagger]: Error getting player info (7bdd007a-3bfd-3764-bae8-a7dd5bd2a42e); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/7bdd007a3bfd3764bae8a7dd5bd2a42e
info: [00:14:27] [ForkJoinPool.commonPool-worker-1/ERROR]: [Feather::TierTagger]: Error getting player info (e9632d21-b98b-3a1a-9a0f-5d68d690b607); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/e9632d21b98b3a1a9a0f5d68d690b607
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1582129968,"cpuUsage":0.0446}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:27] [ForkJoinPool.commonPool-worker-11/ERROR]: [Feather::TierTagger]: Error getting player info (dd709067-248b-3ac7-858b-b8fbda7f46c4); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/dd709067248b3ac7858bb8fbda7f46c4
info: [00:14:27] [ForkJoinPool.commonPool-worker-6/ERROR]: [Feather::TierTagger]: Error getting player info (e31f3f52-a002-39fa-8d27-704420d2d754); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/e31f3f52a00239fa8d27704420d2d754
info: [00:14:27] [ForkJoinPool.commonPool-worker-4/ERROR]: [Feather::TierTagger]: Error getting player info (eb97d1af-687e-3d74-847e-4e7afc6a4f7c); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/eb97d1af687e3d74847e4e7afc6a4f7c
[00:14:27] [ForkJoinPool.commonPool-worker-7/ERROR]: [Feather::TierTagger]: Error getting player info (f1f9e6e5-4e97-30ed-bcd2-31e5773b7376); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/f1f9e6e54e9730edbcd231e5773b7376
info: [00:14:27] [ForkJoinPool.commonPool-worker-9/ERROR]: [Feather::TierTagger]: Error getting player info (b46b5cbe-adf3-302f-823a-bac52df43635); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/b46b5cbeadf3302f823abac52df43635
info: [00:14:27] [ForkJoinPool.commonPool-worker-15/ERROR]: [Feather::TierTagger]: Error getting player info (6835b935-0cf5-39aa-97f1-82a29e6c549b); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/6835b9350cf539aa97f182a29e6c549b
info: [00:14:27] [ForkJoinPool.commonPool-worker-10/ERROR]: [Feather::TierTagger]: Error getting player info (bb1961d8-d012-3054-981e-c7ea77f9dd26); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/bb1961d8d0123054981ec7ea77f9dd26
info: [00:14:27] [ForkJoinPool.commonPool-worker-5/ERROR]: [Feather::TierTagger]: Error getting player info (34d71e72-25a3-35f3-949e-f3b6d87bebb9); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/34d71e7225a335f3949ef3b6d87bebb9
info: [00:14:27] [ForkJoinPool.commonPool-worker-3/ERROR]: [Feather::TierTagger]: Error getting player info (5ab25a2d-6790-3964-b4c8-7d31199b423b); Stack: java.lang.RuntimeException: net.digitalingot.feather.pJ: java.io.IOException: Server returned HTTP response code: 422 for URL: https://mctiers.io/api/rankings/5ab25a2d67903964b4c87d31199b423b
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1984783152,"cpuUsage":0.0391}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":904197200,"cpuUsage":0.0658}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1273258936,"cpuUsage":0.0343}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1609383440,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1978643360,"cpuUsage":0.04}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:32] [Render thread/INFO]: [CHAT]  a Enderman killed [Member] 123VoidPro! 
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":843906736,"cpuUsage":0.0304}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:34] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1177833016,"cpuUsage":0.018}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:14:34] [Render thread/WARN]: Received passengers for unknown entity
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1480616976,"cpuUsage":0.033}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1815892832,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2117882720,"cpuUsage":0.0406}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":848187096,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1250840280,"cpuUsage":0.042}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1636718296,"cpuUsage":0.0296}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2039866728,"cpuUsage":0.042}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":831191952,"cpuUsage":0.0392}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1217065872,"cpuUsage":0.0427}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1586164624,"cpuUsage":0.0428}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2006005688,"cpuUsage":0.0581}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":797165248,"cpuUsage":0.061}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1183041216,"cpuUsage":0.0607}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1535362752,"cpuUsage":0.0258}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1921236672,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":713392040,"cpuUsage":0.0287}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1115728704,"cpuUsage":0.0438}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1435126808,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1753262912,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2038792104,"cpuUsage":0.0142}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":695080160,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":963154856,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1231949016,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1483278944,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1752507144,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2020117416,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":696110240,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:15:02] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] Ana_M0R415 to Lifesteal!
info: [00:15:02] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":963549192,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1231984648,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:15:05] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1483327080,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1752078344,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2020513808,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":677993296,"cpuUsage":0.0161}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":929937120,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:15:10] [Render thread/INFO]: [CHAT] (/cf) ◆ [Ascendant] IFlipCoins made a ↑ Coinflip ↓ for: 5 GC
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1198230824,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1450028128,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1718180688,"cpuUsage":0.0123}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1970130480,"cpuUsage":0.0142}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":628056064,"cpuUsage":0.0151}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":879444008,"cpuUsage":0.0198}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1147877416,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1400070064,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1667973160,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1919631400,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2171563376,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":812055600,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1080486320,"cpuUsage":0.0104}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1332406496,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1600714464,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1852240304,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:15:26] [Render thread/INFO]: [CHAT] \n \n \n瀆  [APPLY FOR STAFF]\n             We are always looking for new staff to\n             join our team. Apply today!\n              ➜ Type /apply to get started!\n
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2137712104,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":796078032,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:15:28] [Render thread/WARN]: Received passengers for unknown entity
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1080923344,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1349358800,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1617794256,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1886229712,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2154665168,"cpuUsage":0.0047}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":813097872,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1081533328,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:15:36] [Render thread/INFO]: [CHAT] ! Hover for the word to type.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1349968784,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1601867864,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1871186488,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2121720720,"cpuUsage":0.0085}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":779607136,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1048042592,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1316717096,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1568375336,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1836813448,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2105246240,"cpuUsage":0.0047}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":763415512,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1031963496,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1300286424,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1568721880,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1837157336,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":2088581496,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":746310960,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1014746416,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1283181872,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1535069240,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:15:56] [Render thread/INFO]: [CHAT] ! Nobody got the answer, it was Zombie.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1803507360,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":813707208,"cpuUsage":0.0324}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1082024528,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1350350352,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1602343960,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1870779440,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":847379216,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1115812624,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:04] [Render thread/INFO]: [CHAT] ぃ #5 ‹ᴀꜱᴛʀᴀ› [iiTwitterii] [Centurion] Chronicallx: gg
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1384515112,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1652950544,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":540019720,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":808457840,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1076976736,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1328548872,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1596799232,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":560401928,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":828653168,"cpuUsage":0.0047}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1080760296,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1349010928,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1617446384,"cpuUsage":0.0085}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":578296688,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":847370264,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1114984544,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1383502064,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1651858120,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":611630032,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":863288272,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1131541056,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1383381960,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:26] [Render thread/WARN]: Received passengers for unknown entity
info: [00:16:26] [Render thread/WARN]: Received passengers for unknown entity
[00:16:26] [Render thread/WARN]: Received passengers for unknown entity
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1652000088,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":611681096,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":880008640,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1131586040,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1400019448,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1668643048,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":594914448,"cpuUsage":0.018}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":863271048,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1131704456,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1400139912,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1652062552,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":628215576,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":879875864,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1148497360,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:41] [Render thread/INFO]: [CHAT] ✔ Teleporting to your clan home at -18794, -53, 12762 in World
info: [00:16:41] [Render thread/INFO]: [CHAT] ◆ Teleporting in 4 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1433521944,"cpuUsage":0.0096}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:42] [Render thread/INFO]: [CHAT] ◆ Teleporting in 3 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1701959448,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:43] [Render thread/INFO]: [CHAT] ◆ Teleporting in 2 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":678934048,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:44] [Render thread/INFO]: [CHAT] ◆ Teleporting in 1 second.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":980831712,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:45] [Render thread/INFO]: Stopping worker threads
info: [00:16:45] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: [00:16:46] [Render thread/WARN]: Not all defined tags for registry ResourceKey[minecraft:root / minecraft:item] are present in data pack: minecraft:music_discs
info: [00:16:46] [Render thread/INFO]: Started 10 worker threads
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1219663448,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1487309296,"cpuUsage":0.0502}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:47] [Render thread/INFO]: [CHAT] ぃ #1 ‹ᴋɪᴛᴛᴇɴꜱ› [Gumball] [Emperor] TheMethod*: meow
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1687771384,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:49] [Render thread/INFO]: [CHAT] ! You have some items pending in /collect!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":651724336,"cpuUsage":0.04}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":820469256,"cpuUsage":0.0533}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1152952128,"cpuUsage":0.0534}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:51] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
[00:16:51] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1425338672,"cpuUsage":0.0305}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1705597760,"cpuUsage":0.043}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":741153792,"cpuUsage":0.0744}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1028073096,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1310955296,"cpuUsage":0.0268}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:16:56] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
info: [00:16:57] [Render thread/WARN]: Received passengers for unknown entity
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1579390752,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":571971184,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":841928640,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1095570976,"cpuUsage":0.022}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:01] [Render thread/INFO]: [CHAT] Lifesteal [Apprentice] omnipotentgamer6 ➜ You: shadow has sznl on koth rn
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1394442200,"cpuUsage":0.0277}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1646202952,"cpuUsage":0.0353}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":659981592,"cpuUsage":0.0277}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":895971808,"cpuUsage":0.0245}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1179300744,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1498069896,"cpuUsage":0.0344}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1800059784,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":808644288,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1078911096,"cpuUsage":0.0201}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1329960496,"cpuUsage":0.0495}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1569056560,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":573217240,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":875207128,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1211047680,"cpuUsage":0.0258}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1529814784,"cpuUsage":0.0296}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":577089800,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":858734984,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:18] [Render thread/WARN]: Blank message was attempted to be sent.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1109232992,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1394691384,"cpuUsage":0.0314}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:20] [Render thread/INFO]: [CHAT] RUNES ✔ Your Rest V is now active.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1696679048,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":742184368,"cpuUsage":0.0304}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1044174256,"cpuUsage":0.0361}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1313767600,"cpuUsage":0.0239}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:24] [Render thread/INFO]: [CHAT] RUNES ✔ [Apprentice] the_shadow606's Leaden II was applied to: Godlike Sword! (76% chance)
info: [00:17:25] [Render thread/INFO]: [CHAT] - $475 1x Splash Potion ($13,569,187.42 ➜ $13,568,712.42)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1597824432,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":659426680,"cpuUsage":0.0362}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":961198160,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1245406528,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:29] [Render thread/INFO]: [CHAT] WARP ✔ Warping to Pvp...
info: [00:17:29] [Render thread/INFO]: [CHAT] ◆ Teleporting in 4 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1531841920,"cpuUsage":0.0238}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:29] [Render thread/INFO]: [CHAT]  [Member] Shad0www killed [Member] .NeedyWater42671 with [Gladiator Sword]! 
info: [00:17:30] [Render thread/INFO]: [CHAT] ◆ Teleporting in 3 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1805203600,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:31] [Render thread/INFO]: [CHAT] ◆ Teleporting in 2 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":824604928,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:32] [Render thread/INFO]: [CHAT] ◆ Teleporting in 1 second.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1143569064,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:33] [Render thread/INFO]: Stopping worker threads
info: [00:17:33] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1413187320,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:33] [Render thread/WARN]: Not all defined tags for registry ResourceKey[minecraft:root / minecraft:item] are present in data pack: minecraft:music_discs
info: [00:17:33] [Render thread/INFO]: Started 10 worker threads
info: [00:17:33] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1751275176,"cpuUsage":0.0085}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":867063504,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:35] [Worker-Main-16/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
info: at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-8/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-1/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-16/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-8/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
info: at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-1/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-16/ERROR]: Could not decode textures payload
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:136) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-8/ERROR]: Could not decode textures payload
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:136) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:17:35] [Worker-Main-1/ERROR]: Could not decode textures payload
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:136) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1117076272,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:36] [Render thread/INFO]: [CHAT] ! You have some items pending in /collect!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1402288936,"cpuUsage":0.0296}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1637688616,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: [00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
[00:17:38] [Render thread/WARN]: Requested creation of existing team '00231themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":704037808,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1019998936,"cpuUsage":0.019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1359792568,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:41] [Render thread/INFO]: Stopping worker threads
info: [00:17:41] [Render thread/INFO]: Started 10 worker threads
info: [00:17:41] [Render thread/INFO]: Stopping worker threads
info: [00:17:41] [Render thread/INFO]: Started 10 worker threads
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1678340624,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":752027600,"cpuUsage":0.0287}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:44] [Render thread/INFO]: [CHAT] RUNES ✔ Stepped! Your Sky Stepper III is usable again in: 9s
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":952375048,"cpuUsage":0.0325}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:45] [Render thread/INFO]: [CHAT] RUNES ✔ Dashed! Your Dasher III is usable again in: 9s
info: [00:17:45] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:17:45] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1204313840,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:46] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:17:46] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: [00:17:46] [Render thread/INFO]: [CHAT] RUNES ✔ Your Lifeforce III has restored 3 ❤!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1539575560,"cpuUsage":0.0239}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":756311456,"cpuUsage":0.0419}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1108419368,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:48] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:17:48] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: [00:17:49] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:17:49] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: [00:17:49] [Render thread/INFO]: [CHAT] (/cf) ◆ [Ascendant] IFlipCoins made a ↑ Coinflip ↓ for: 5 GC
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1477488896,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1813033216,"cpuUsage":0.0454}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:50] [Render thread/INFO]: [CHAT] Healed!
info: [00:17:51] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:17:51] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":904618240,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1240162560,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:53] [Render thread/INFO]: [CHAT] RUNES ✔ Your Sky Stepper III may now be used!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1575706880,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:54] [Render thread/INFO]: [CHAT] RUNES ✔ Your Dasher III may now be used!
info: [00:17:54] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:17:54] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":669258240,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:17:55] [Render thread/INFO]: [CHAT] ! Players nearby: none
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1022423952,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1340344832,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1675889152,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":724243984,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1026233872,"cpuUsage":0.0123}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1344998928,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1680543248,"cpuUsage":0.0229}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":736175504,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1055062608,"cpuUsage":0.019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:04] [Render thread/INFO]: [CHAT] ぃ #5 ‹ᴀꜱᴛʀᴀ› [iiTwitterii] [Centurion] Chronicallx: why you guys aint saying gg back 
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1390869416,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1709254032,"cpuUsage":0.0209}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":805134136,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1140678456,"cpuUsage":0.0209}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1459755992,"cpuUsage":0.0257}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1795186912,"cpuUsage":0.0396}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":905765776,"cpuUsage":0.0171}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1241310096,"cpuUsage":0.0351}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1593631632,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":687918784,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:14] [Render thread/INFO]: [CHAT] ✔ Teleporting to your clan home at -18794, -53, 12762 in World
info: [00:18:14] [Render thread/INFO]: [CHAT] ◆ Teleporting in 4 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1040172176,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:15] [Render thread/INFO]: [CHAT] ◆ Teleporting in 3 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1375789816,"cpuUsage":0.0475}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:16] [Render thread/INFO]: [CHAT] ◆ Teleporting in 2 seconds.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1711260816,"cpuUsage":0.0333}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:17] [Render thread/INFO]: [CHAT] ◆ Teleporting in 1 second.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":805132656,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:18] [Render thread/INFO]: Stopping worker threads
info: [00:18:19] [Render thread/INFO]: Found non-pack entry 'C:\Users\<USER>\AppData\Roaming\.minecraft\resourcepacks\font\default.json', ignoring
info: [00:18:19] [Render thread/WARN]: Not all defined tags for registry ResourceKey[minecraft:root / minecraft:item] are present in data pack: minecraft:music_discs
info: [00:18:19] [Render thread/INFO]: Started 10 worker threads
info: [00:18:19] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
info: [00:18:19] [Render thread/INFO]: [CHAT] ぃ #4 ‹ᴅᴇᴍᴏɴᴀʀᴋ› [Darkrai] [Apprentice] omnipotentgamer6: cause shadow came down
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":654098080,"cpuUsage":0.029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":921848912,"cpuUsage":0.0324}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1143095032,"cpuUsage":0.0296}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:21] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
info: [00:18:21] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
info: [00:18:22] [Render thread/INFO]: [CHAT] ! You have some items pending in /collect!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1308039784,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1492922208,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1743624200,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":694439280,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":913525352,"cpuUsage":0.0199}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1130649528,"cpuUsage":0.0151}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1348091640,"cpuUsage":0.0247}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1573730648,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1818021600,"cpuUsage":0.0228}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":790905656,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":992232248,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1194531640,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1379083904,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1512860416,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1679365552,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":676855680,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":878890744,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1046349384,"cpuUsage":0.0159}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1229423744,"cpuUsage":0.017}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1430949856,"cpuUsage":0.0329}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:42] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] Garmshift to Lifesteal!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1665830872,"cpuUsage":0.0218}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1850380256,"cpuUsage":0.0171}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":794447704,"cpuUsage":0.0226}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":978705544,"cpuUsage":0.0209}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1181912256,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1320597064,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1466565496,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1598761976,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1818963664,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:52] [Render thread/INFO]: [Feather::LifetimeManager]: atlas/discordCloak was destroyed
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":693464224,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:18:53] [Render thread/INFO]: [CHAT] あ [Member] BURGER_0: wolf u got coords
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":844744920,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":993039248,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1147093008,"cpuUsage":0.0293}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1295029136,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1449018264,"cpuUsage":0.0028}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1598814320,"cpuUsage":0.0257}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1765078856,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":643863344,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":795742720,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":948580816,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1099166800,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1263659808,"cpuUsage":0.0229}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1399313904,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1550821568,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1701304344,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":615556832,"cpuUsage":0.0096}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":795463376,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":965483176,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1098052416,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1285517544,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1450707352,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1618759752,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1753558504,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":661428592,"cpuUsage":0.0096}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:19:18] [Render thread/INFO]: [Feather::LifetimeManager]: atlas/betaSpace was destroyed
info: [00:19:19] [Render thread/INFO]: [Feather::LifetimeManager]: atlas/spring2025BeeHat was destroyed
info: [00:19:19] [Render thread/INFO]: [Feather::LifetimeManager]: atlas/spring2025BeeAura was destroyed
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":862276624,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1114413424,"cpuUsage":0.0277}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1350936280,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1518658888,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1691605928,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1868909584,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":758858144,"cpuUsage":0}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":914602184,"cpuUsage":0.0132}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1094402464,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1279673984,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1530614784,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1717013944,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":642707416,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":826393936,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1011327176,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1184542424,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:19:35] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:19:35] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a COMMON Sky Stepper III Rune from 71% to: 86%!
info: [00:19:35] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:19:35] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a COMMON Sky Stepper III Rune from 86% to: 100%!
info: [00:19:35] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1364659720,"cpuUsage":0.0066}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:19:35] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: [00:19:35] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: [00:19:36] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1582590496,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1735100000,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":643348048,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":892959784,"cpuUsage":0.0257}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1262058536,"cpuUsage":0.0315}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1565773744,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1866038312,"cpuUsage":0.0369}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":895218584,"cpuUsage":0.0429}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1163670744,"cpuUsage":0.0323}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1350950592,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1518956968,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1685327312,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1853880016,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":764815808,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":998254384,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1232862008,"cpuUsage":0.019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1467743032,"cpuUsage":0.0142}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1702624056,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:19:54] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] SkibidiAnton to Lifesteal!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":695304392,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":930195664,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1198368680,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1433249704,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1634828480,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:19:59] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:19:59] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1804133864,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":727822632,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":929147176,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:02] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1096941560,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1317765264,"cpuUsage":0.0258}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1534623800,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1719182472,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":676694832,"cpuUsage":0.0315}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":913665272,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:09] [Render thread/INFO]: [CHAT] ! Players nearby: IWannaEat_(13m), Clariah(26m)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1064105880,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1214843992,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1399384608,"cpuUsage":0.0182}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1616484512,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1803316064,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":663301600,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":831065104,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:16] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:20:16] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a RARE Dasher III Rune from 80% to: 90%!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1015614496,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:16] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:20:16] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a RARE Dasher III Rune from 90% to: 100%!
info: [00:20:16] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: [00:20:17] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: [00:20:17] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: [00:20:17] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1217252960,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1418579552,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1619906136,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1802742096,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":712377696,"cpuUsage":0.0096}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":885471592,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1064395736,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:24] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:20:24] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a RARE Spring Shoes III Rune from 61% to: 71%!
info: [00:20:24] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:20:24] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a RARE Spring Shoes III Rune from 71% to: 81%!
info: [00:20:24] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:20:24] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a RARE Spring Shoes III Rune from 81% to: 91%!
info: [00:20:24] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:20:24] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a RARE Spring Shoes III Rune from 91% to: 100%!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1249248608,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:24] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: [00:20:25] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: [00:20:25] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1483828184,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1735484376,"cpuUsage":0.0257}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:27] [Render thread/INFO]: [CHAT] あ [Member] BURGER_0: wolfllet metake picshot wait
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":650420184,"cpuUsage":0.0151}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":853749824,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1032265592,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:30] [Render thread/INFO]: [CHAT] RUNES ✔ Your Spring Shoes III was applied to: 💘 Cupid Boots 💘! (100% chance)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1252116096,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1456517800,"cpuUsage":0.0277}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1655042008,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1887911904,"cpuUsage":0.0296}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":797752992,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1016129760,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1234222864,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1435287200,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1603059360,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1787879048,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":681494544,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:41] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] MaRK716 to Lifesteal!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":899335416,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1100914432,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1301988600,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1503315192,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1688124840,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1889451440,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":831575896,"cpuUsage":0.0047}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1049927840,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1251685176,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:50] [Render thread/INFO]: [CHAT] あ [Member] BURGER_0: where are u
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1368694944,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1487581744,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1603568272,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1788125336,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":680691136,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":898527592,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:20:57] [Render thread/INFO]: [CHAT] ! Unscramble the word ciuymlem.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1099856232,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1301180776,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1487284992,"cpuUsage":0.0096}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1720878528,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":615893176,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":731503992,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":965679408,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1084143944,"cpuUsage":0.0123}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1190615504,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1301479352,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1452228368,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:09] [Render thread/WARN]: Requested creation of existing team '00229themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1637277248,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1792531000,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:11] [Render thread/INFO]: [CHAT]
info: [00:21:11] [Render thread/INFO]: [CHAT]  $ STORE $ [Member] .BpadronnX purchased the 5x Lunar Key on Factions! GG! (/store)
info: [00:21:11] [Render thread/INFO]: [CHAT]
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":686269216,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":854583040,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:12] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:21:12] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a LEGENDARY Rocket Boots II Rune from 84% to: 87%!
info: [00:21:13] [Render thread/WARN]: Received passengers for unknown entity
info: [00:21:13] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:21:13] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a LEGENDARY Rocket Boots II Rune from 87% to: 90%!
info: [00:21:13] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:21:13] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a LEGENDARY Rocket Boots II Rune from 90% to: 93%!
info: [00:21:13] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:21:13] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a LEGENDARY Rocket Boots II Rune from 93% to: 96%!
info: [00:21:13] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:21:13] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a LEGENDARY Rocket Boots II Rune from 96% to: 99%!
info: [00:21:13] [Render thread/INFO]: [CHAT] - 1x ☘ Lucky Gem ☘
info: [00:21:13] [Render thread/INFO]: [CHAT] RUNES ✔ You've increased the success chance of a LEGENDARY Rocket Boots II Rune from 99% to: 100%!
info: [00:21:13] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1053904056,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:14] [Render thread/INFO]: [CHAT] RUNES ✘ This rune is already at 100% success chance!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1188672376,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1321417224,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:16] [Render thread/INFO]: [CHAT] あ [Member] BURGER_0: wait
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1455634944,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:17] [Render thread/INFO]: [CHAT] ! Nobody got the answer, it was Mycelium.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1573617112,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1706751528,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1826748248,"cpuUsage":0.0247}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":652526232,"cpuUsage":0.0142}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":768347032,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":885164904,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1003228056,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1122349240,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1238109080,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1356094712,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1573653408,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1692267504,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1827124224,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":651981144,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":871621496,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":988737960,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:35] [Render thread/INFO]: [CHAT] RUNES ✔ Your Rest V is now active.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1121697200,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1239137712,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1356925448,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1473272256,"cpuUsage":0.0104}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1641044408,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1829358312,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":702077576,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":936958600,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:44] [Render thread/INFO]: [CHAT] あ [Member] BURGER_0: doneare u inmdian?
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1139629416,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1272502920,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1424339656,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1608047240,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1793438408,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:21:48] [Render thread/WARN]: Requested creation of existing team '00234themethodue'
[00:21:48] [Render thread/WARN]: Requested creation of existing team '00234themethodue'
[00:21:48] [Render thread/WARN]: Requested creation of existing team '00234themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":634462112,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":803244984,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":988920504,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1171330976,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1341231568,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1527260656,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1674647456,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1875974048,"cpuUsage":0.0066}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":788735408,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":973012144,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1172345888,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1358368872,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1575509288,"cpuUsage":0.0543}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1729587376,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1865642104,"cpuUsage":0.0228}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":704203928,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":853758584,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1004751480,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1194715728,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1360902080,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1509510320,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1692629016,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1894644496,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":803693184,"cpuUsage":0.0182}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":989828152,"cpuUsage":0.0201}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1190199544,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1375704136,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1575456976,"cpuUsage":0.0096}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1762684168,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":652364888,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":836914248,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1022894680,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1207464952,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1407339600,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1591879776,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1776427104,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":654132592,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":857193336,"cpuUsage":0.0085}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1062073568,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:22:29] [Render thread/INFO]: [CHAT] RUNES ✔ Your Rocket Boots II was applied to: 🐝 Stinger Boots 🐝! (100% chance)
info: [00:22:29] [Render thread/INFO]: [CHAT] RUNES ✔ [Emperor] TheMethod*'s Rocket Boots II was applied to: 🐝 Stinger Boots 🐝! (100% chance)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1224970144,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:22:30] [Render thread/INFO]: [CHAT] RUNES ✔ Your Dasher III was applied to: 🐝 Stinger Boots 🐝! (100% chance)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1394563080,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:22:30] [Render thread/INFO]: [CHAT] RUNES ✔ Your Sky Stepper III was applied to: 🐝 Stinger Boots 🐝! (100% chance)
info: [00:22:31] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] .GamingboyMM to Lifesteal!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1628945272,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1845305872,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":786913104,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":990050432,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1223477480,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1461261192,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1661136424,"cpuUsage":0.0229}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1862463328,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":791326664,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1024096632,"cpuUsage":0.018}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1227101064,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1460607392,"cpuUsage":0.0372}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1662298016,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1812928936,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":636552888,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":791193744,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":921765560,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1040712016,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:22:49] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] f01_msmc_sbf to Lifesteal!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1190695768,"cpuUsage":0.0066}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1308961216,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1462961424,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1609288464,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1732248856,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1900164688,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":754260352,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":938449336,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1140136320,"cpuUsage":0.0381}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1299101416,"cpuUsage":0.0264}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1428733152,"cpuUsage":0.0396}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1561584616,"cpuUsage":0.0227}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1697644296,"cpuUsage":0.0246}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:03] [Render thread/WARN]: Requested creation of existing team '00234themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1844419000,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":687352968,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":823750392,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":957821336,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1106425296,"cpuUsage":0.0132}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1225900784,"cpuUsage":0.0085}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1376895744,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1609741776,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1763275376,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":625298776,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:13] [Render thread/INFO]: [CHAT] ⚠ Your Boots' runes are warming up . . . (7.5s)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":856517304,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":994394792,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1108173496,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1244058688,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1397386824,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1595563792,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:19] [Render thread/ERROR]: Deleting stream buffers: Invalid operation.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1812816568,"cpuUsage":0.0286}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:20] [Render thread/INFO]: [CHAT] ✔ Your Boots' runes are now ready!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":720829520,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":938608664,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1157044224,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1408370712,"cpuUsage":0.0258}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1643251736,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1896613760,"cpuUsage":0.0448}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":888517688,"cpuUsage":0.0513}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1125508920,"cpuUsage":0.0448}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1360058112,"cpuUsage":0.0229}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1609935928,"cpuUsage":0.0381}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1744153656,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1895788224,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":755961696,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":939984784,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1091808504,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:36] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:23:36] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1241974680,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1375930120,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1527195736,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1714531896,"cpuUsage":0.0123}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1847483184,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":738981648,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:41] [Worker-Main-15/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
info: [00:23:41] [Worker-Main-15/ERROR]: Malformed signature encoding on property Property[name=textures, value=undefined, signature=undefined]
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo.validateProperty(YggdrasilServicesKeyInfo.java:158) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.lambda$getPropertySignatureState$0(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at java.base/java.util.stream.MatchOps$1MatchSink.accept(Unknown Source) ~[?:?]
	at java.base/java.util.AbstractList$RandomAccessSpliterator.tryAdvance(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.forEachWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyIntoWithCancel(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.copyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.MatchOps$MatchOp.evaluateSequential(Unknown Source) ~[?:?]
	at java.base/java.util.stream.AbstractPipeline.evaluate(Unknown Source) ~[?:?]
	at java.base/java.util.stream.ReferencePipeline.noneMatch(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.getPropertySignatureState(YggdrasilMinecraftSessionService.java:189) ~[authlib-6.0.54.jar:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:132) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
[00:23:41] [Worker-Main-15/ERROR]: Could not decode textures payload
java.lang.IllegalArgumentException: Last unit does not have enough valid bits
	at java.base/java.util.Base64$Decoder.decode0(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at java.base/java.util.Base64$Decoder.decode(Unknown Source) ~[?:?]
	at knot/com.mojang.authlib.yggdrasil.YggdrasilMinecraftSessionService.unpackTextures(YggdrasilMinecraftSessionService.java:136) ~[authlib-6.0.54.jar:?]
	at knot/net.minecraft.class_1071$1.method_54647(class_1071.java:55) ~[client-intermediary.jar:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(Unknown Source) [?:?]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.scan(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(Unknown Source) [?:?]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(Unknown Source) [?:?]
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":923842976,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1108900592,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1309728288,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1511054880,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:45] [Render thread/INFO]: [CHAT] (/cf) ◆ [Ascendant] Aviizz made a ↑ Coinflip ↓ for: 7 GC
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1729158688,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":623652560,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:47] [Render thread/INFO]: [CHAT] (/binds) ✔ Use an ability by pressing the corresponding hotkey.
info: [00:23:47] [Render thread/INFO]: [CHAT] Press Double Tap R to cancel.
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":742694160,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:23:49] [Render thread/INFO]: [CHAT] RUNES ✔ Your Honey Swirl V summoned a large vortex of honey for 15 seconds!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":894968976,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1043082976,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1193416120,"cpuUsage":0.0028}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1377019648,"cpuUsage":0.0047}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1528335104,"cpuUsage":0.0066}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1699294928,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1865477864,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":740829848,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":975710872,"cpuUsage":0.0305}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1160744472,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1311255192,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1496288792,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1664623688,"cpuUsage":0.0133}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1848600416,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":725698048,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":874994592,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1009677912,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1143418144,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1263530952,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1401121616,"cpuUsage":0.0047}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1533162832,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1698763944,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1871469600,"cpuUsage":0.0239}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":726274184,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1010095040,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1328526992,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1613757296,"cpuUsage":0.0229}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1814560672,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":726148024,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":925027792,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1143119920,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1344874920,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1545773104,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1699648432,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1849380856,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":691758432,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:24:26] [Render thread/INFO]: [CHAT] あ #395 ‹ᴍᴀx¹²¹› [Member] GodTagger: Yo anyone know how to make a heart
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":875109656,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1076438296,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1278155272,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1464216704,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1665150200,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1848188184,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":725625808,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":908654976,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1094208936,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1262496720,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1430268880,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:24:37] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] SpeedKid2020 to Lifesteal!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1612789784,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1798861408,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":673168720,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":840928992,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1042714152,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1232121232,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1411354336,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1596391216,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1780453088,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":658472024,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":842556912,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1027117984,"cpuUsage":0.0085}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1195340280,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1363124792,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1547199984,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1715433984,"cpuUsage":0.0277}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1883206136,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":775156008,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":944672624,"cpuUsage":0.0229}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1161893528,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1364310560,"cpuUsage":0.0277}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1546925064,"cpuUsage":0.0372}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1733663656,"cpuUsage":0.0382}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1901276776,"cpuUsage":0.021}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":793027864,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":976916184,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1162100080,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1331820896,"cpuUsage":0.0182}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1513787096,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1701920560,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1906185224,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":793791448,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1010315288,"cpuUsage":0.0219}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1228430848,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:11] [Render thread/INFO]: [CHAT] ! You're about to drop an expensive item! Drop it again to confirm this action. - /toggledropwarn to disable this warning
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1465935136,"cpuUsage":0.0362}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1647849496,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:14] [Render thread/INFO]: [CHAT] (/cf) ◆ [Ascendant➕] Milodias made a ↑ Coinflip ↓ for: 1 GC
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1850489768,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":741796392,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":943122984,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1129438808,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1312905280,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1496782432,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1681322536,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1866553408,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":728655728,"cpuUsage":0.0208}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":909762648,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1078219128,"cpuUsage":0.0171}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1264140136,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1471380704,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1681514584,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1882841176,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":776656648,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":962620096,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1148473864,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1330989952,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1515520784,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:35] [Render thread/INFO]: [CHAT] RUNES ⚠ IWannaEat_'s Honey Swirl V summoned a large vortex of honey for 15 seconds! Enemies caught inside of it are inflicted with Slowness V!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1700086072,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:35] [Render thread/INFO]: [CHAT] あ [Member] BURGER_0: darwoilf wait i gtg u go 1 min onlyy
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1900040528,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":795524832,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":996338016,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1211933256,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1380438928,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1565512904,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1782358600,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":676716472,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":878702496,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1064438744,"cpuUsage":0.0161}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:46] [Render thread/INFO]: [CHAT] Screenshot saved [Open] [Copy] [Upload] [Tweet]
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1248338144,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1435799400,"cpuUsage":0.0237}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1619778560,"cpuUsage":0.0285}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1817567152,"cpuUsage":0.0276}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":708687984,"cpuUsage":0.0314}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":926802976,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:51] [Render thread/INFO]: [CHAT] ⚠ Your Boots' runes are warming up . . . (7.5s)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1097901904,"cpuUsage":0.0216}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1245570080,"cpuUsage":0.02}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1413331056,"cpuUsage":0.0304}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1564946840,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:56] [Render thread/INFO]: [CHAT] (/cf) ◆ [Ascendant] IFlipCoins made a ↑ Coinflip ↓ for: 5 GC
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1748875376,"cpuUsage":0.0304}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":642853048,"cpuUsage":0.0324}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:25:58] [Render thread/INFO]: [CHAT] ✔ Your Boots' runes are now ready!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":846573368,"cpuUsage":0.0341}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1028738208,"cpuUsage":0.0267}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1213868160,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1397827768,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1568087240,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1735931440,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":626748680,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":776339144,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":943520680,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1097096632,"cpuUsage":0.0181}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1266270600,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1447427784,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1635360144,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1833906928,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:12] [Render thread/INFO]: [CHAT] \n \n \n瀆  [SUPPORT THE SERVER]\n             Enjoying Complex Gaming? Support the\n             server and get cool perks too!\n              ➜ Visit our store at /store!\n
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":732903016,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":929788496,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1132407792,"cpuUsage":0.0248}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1336319976,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1535013576,"cpuUsage":0.0066}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1718305400,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1887373032,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":777210568,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:20] [Render thread/INFO]: [CHAT] ! Craft a Cherry Trapdoor! (Click Here!)
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":944985496,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1112754888,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:23] [Render thread/WARN]: Requested creation of existing team '00234themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1299581312,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1498632904,"cpuUsage":0.0277}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1700780016,"cpuUsage":0.0153}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1901295352,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":827702056,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1047199376,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1230355240,"cpuUsage":0.0114}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1432410384,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1632999232,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1817548608,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":728065392,"cpuUsage":0.0009}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":929391984,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1113930424,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1319406288,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1516583608,"cpuUsage":0.0152}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:38] [Render thread/INFO]: [CHAT] ! TheDarkWolf3221 got the answer Cherry Trapdoor in 19 seconds!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1718474904,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":628756904,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":835761568,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1046871592,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:42] [Render thread/INFO]: [CHAT]  ◆ Welcome [Member] .XKMeSteRLL to Lifesteal!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1299513496,"cpuUsage":0.0238}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1519508096,"cpuUsage":0.0305}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1751514664,"cpuUsage":0.0353}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":677943664,"cpuUsage":0.0401}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":932350072,"cpuUsage":0.0486}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:47] [Render thread/INFO]: [CHAT]  a Creeper blew up [Member] .Carloboss_2014! 
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1164493392,"cpuUsage":0.0171}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1401166936,"cpuUsage":0.0191}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1601079144,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1785620104,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":662276208,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":831705472,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":999469304,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:54] [Render thread/WARN]: Requested creation of existing team '00239themethodue'
[00:26:54] [Render thread/WARN]: Requested creation of existing team '00239themethodue'
info: [00:26:54] [Render thread/WARN]: Requested creation of existing team '00239themethodue'
[00:26:54] [Render thread/WARN]: Requested creation of existing team '00239themethodue'
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1205981384,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:56] [Render thread/INFO]: [CHAT] あ [Member] RK_king: burger tea
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1366499544,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:26:56] [Render thread/INFO]: [CHAT] RUNES ✔ Stepped! Your Sky Stepper III is usable again in: 9s
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1517486144,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1669257000,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1819484376,"cpuUsage":0.0019}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:27:00] [Render thread/INFO]: [CHAT] あ [Member] RK_king: burger team
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":668884816,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":833186504,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1035252816,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1183691832,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1335298168,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1484330192,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:27:05] [Render thread/INFO]: [CHAT] RUNES ✔ Your Sky Stepper III may now be used!
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1670240264,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1838012424,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":699461752,"cpuUsage":0.0172}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":881942952,"cpuUsage":0.0115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:27:10] [Render thread/INFO]: [CHAT] あ ‹ʙᴇᴀʀ› [Member] TheDarkWolf3221: ?
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1067094168,"cpuUsage":0.0038}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1251643544,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1452970128,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1638913648,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1838982656,"cpuUsage":0.0143}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":769181512,"cpuUsage":0.0162}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":936990992,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1106671992,"cpuUsage":0.0067}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1272525120,"cpuUsage":0.0124}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1420972576,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1573953432,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1739406392,"cpuUsage":0.0105}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1857383352,"cpuUsage":0.0057}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":699932224,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":854039600,"cpuUsage":0.0029}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":983552784,"cpuUsage":0.001}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1171230008,"cpuUsage":0.0134}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1352133832,"cpuUsage":0.0095}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [00:27:28] [Render thread/INFO]: [CHAT] Unknown or incomplete command, see below for error\n5<--[HERE]
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1536685256,"cpuUsage":0.0076}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1704993384,"cpuUsage":0.0086}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":648170752,"cpuUsage":0.0306}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":883051776,"cpuUsage":0.0229}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222564743,"state":{"Stats":{"memoryUsage":1034503400,"cpuUsage":0.0048}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [2025-05-22T07:27:34Z ERROR rust_launcher::messaging::channel] error during messaging worker: An existing connection was forcibly closed by the remote host. (os error 10054)
