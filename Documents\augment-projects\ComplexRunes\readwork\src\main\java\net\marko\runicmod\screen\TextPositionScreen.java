package net.marko.runicmod.screen;

import net.marko.runicmod.RunicMod;
import net.marko.runicmod.config.RunicModConfig;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.SliderWidget;
import net.minecraft.client.gui.widget.TextWidget;
import net.minecraft.text.Text;
import org.slf4j.Logger;

/**
 * Screen for adjusting text position
 */
public class TextPositionScreen extends Screen {
    private static final Logger LOGGER = RunicMod.LOGGER;

    private final Screen parent;
    private final RunicModConfig config;

    // Widgets
    private ButtonWidget backButton;
    private SliderWidget xSlider;
    private SliderWidget ySlider;
    private SliderWidget scaleSlider;
    private ButtonWidget resetButton;
    private ButtonWidget centerButton;

    // Dragging state
    private boolean isDragging = false;
    private int dragStartX;
    private int dragStartY;
    private int originalX;
    private int originalY;

    // Example text
    private String exampleText = "PLAYER IS RUNICED FOR 5";

    public TextPositionScreen(Screen parent) {
        super(Text.literal("Text Position"));
        this.parent = parent;
        this.config = RunicModConfig.getInstance();
    }

    @Override
    protected void init() {
        super.init();

        int buttonWidth = 150;
        int buttonHeight = 20;
        int centerX = width / 2;
        int startY = height - 30;
        int spacing = 25;

        // Add back button
        backButton = ButtonWidget.builder(Text.literal("Save & Back"), button -> {
            client.setScreen(parent);
        }).dimensions(centerX - buttonWidth / 2, startY, buttonWidth, buttonHeight).build();
        addDrawableChild(backButton);

        // Add X position slider
        xSlider = new SliderWidget(centerX - buttonWidth / 2, startY - spacing * 4, buttonWidth, buttonHeight,
                Text.literal("X Position: " + config.getTitleX()),
                (double) config.getTitleX() / width) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("X Position: " + config.getTitleX()));
            }

            @Override
            protected void applyValue() {
                config.setTitleX((int) (value * width));
                updateMessage();
            }
        };
        addDrawableChild(xSlider);

        // Add Y position slider
        ySlider = new SliderWidget(centerX - buttonWidth / 2, startY - spacing * 3, buttonWidth, buttonHeight,
                Text.literal("Y Position: " + config.getTitleY()),
                (double) config.getTitleY() / height) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Y Position: " + config.getTitleY()));
            }

            @Override
            protected void applyValue() {
                config.setTitleY((int) (value * height));
                updateMessage();
            }
        };
        addDrawableChild(ySlider);

        // Add scale slider
        scaleSlider = new SliderWidget(centerX - buttonWidth / 2, startY - spacing * 2, buttonWidth, buttonHeight,
                Text.literal("Scale: " + String.format("%.2f", config.getTitleScale())),
                (config.getTitleScale() - 0.5) / 1.5) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Scale: " + String.format("%.2f", config.getTitleScale())));
            }

            @Override
            protected void applyValue() {
                config.setTitleScale((float) (0.5 + value * 1.5));
                updateMessage();
            }
        };
        addDrawableChild(scaleSlider);

        // Add reset button
        resetButton = ButtonWidget.builder(Text.literal("Reset Position"), button -> {
            config.setTitleX(-1); // Center
            config.setTitleY(20); // Default Y
            config.setTitleScale(1.0f); // Default scale
            updateSliders();
        }).dimensions(centerX - buttonWidth - 5, startY - spacing, buttonWidth, buttonHeight).build();
        addDrawableChild(resetButton);

        // Add center button
        centerButton = ButtonWidget.builder(Text.literal("Center Horizontally"), button -> {
            config.setTitleX(-1); // Center
            updateSliders();
        }).dimensions(centerX + 5, startY - spacing, buttonWidth, buttonHeight).build();
        addDrawableChild(centerButton);

        // Add instructions
        addDrawableChild(ButtonWidget.builder(
                Text.literal("Drag the text to position it or use the sliders below"),
                button -> {})
                .dimensions(centerX - 200, 20, 400, 20)
                .build());

        // Add drag instructions
        addDrawableChild(ButtonWidget.builder(
                Text.literal("Click and drag the example text below"),
                button -> {})
                .dimensions(centerX - 200, 45, 400, 20)
                .build());
    }

    /**
     * Updates the sliders to match the current config values
     */
    private void updateSliders() {
        // We can't directly update the sliders, so we'll recreate them
        remove(xSlider);
        remove(ySlider);
        remove(scaleSlider);

        int buttonWidth = 150;
        int buttonHeight = 20;
        int centerX = width / 2;
        int startY = height - 30;
        int spacing = 25;

        int x = config.getTitleX();
        if (x == -1) {
            // Calculate centered position for display
            MinecraftClient client = MinecraftClient.getInstance();
            x = (width / 2) - (client.textRenderer.getWidth(exampleText) / 2);
        }

        // Recreate X position slider
        xSlider = new SliderWidget(centerX - buttonWidth / 2, startY - spacing * 4, buttonWidth, buttonHeight,
                Text.literal("X Position: " + x),
                (double) x / width) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("X Position: " + config.getTitleX()));
            }

            @Override
            protected void applyValue() {
                config.setTitleX((int) (value * width));
                updateMessage();
            }
        };
        addDrawableChild(xSlider);

        // Recreate Y position slider
        ySlider = new SliderWidget(centerX - buttonWidth / 2, startY - spacing * 3, buttonWidth, buttonHeight,
                Text.literal("Y Position: " + config.getTitleY()),
                (double) config.getTitleY() / height) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Y Position: " + config.getTitleY()));
            }

            @Override
            protected void applyValue() {
                config.setTitleY((int) (value * height));
                updateMessage();
            }
        };
        addDrawableChild(ySlider);

        // Recreate scale slider
        scaleSlider = new SliderWidget(centerX - buttonWidth / 2, startY - spacing * 2, buttonWidth, buttonHeight,
                Text.literal("Scale: " + String.format("%.2f", config.getTitleScale())),
                (config.getTitleScale() - 0.5) / 1.5) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Scale: " + String.format("%.2f", config.getTitleScale())));
            }

            @Override
            protected void applyValue() {
                config.setTitleScale((float) (0.5 + value * 1.5));
                updateMessage();
            }
        };
        addDrawableChild(scaleSlider);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        renderBackground(context, mouseX, mouseY, delta);

        // Draw the example text
        int x = config.getTitleX();
        int y = config.getTitleY();
        float scale = config.getTitleScale();

        // Center the text if x is -1
        if (x == -1) {
            x = (width / 2) - (textRenderer.getWidth(exampleText) / 2);
        }

        // Draw a box around the text area
        int textWidth = (int) (textRenderer.getWidth(exampleText) * scale);
        int textHeight = (int) (textRenderer.fontHeight * scale);
        context.fill(x - 2, y - 2, x + textWidth + 2, y + textHeight + 2, 0x80FFFFFF);

        // Draw the text
        context.getMatrices().push();
        context.getMatrices().scale(scale, scale, 1.0f);
        context.drawText(textRenderer, exampleText, (int) (x / scale), (int) (y / scale), config.getTitleColor(), true);
        context.getMatrices().pop();

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            int x = config.getTitleX();
            int y = config.getTitleY();
            float scale = config.getTitleScale();

            // Center the text if x is -1
            if (x == -1) {
                x = (width / 2) - (textRenderer.getWidth(exampleText) / 2);
            }

            // Calculate text bounds
            int textWidth = (int) (textRenderer.getWidth(exampleText) * scale);
            int textHeight = (int) (textRenderer.fontHeight * scale);

            // Check if mouse is over the text
            if (mouseX >= x && mouseX <= x + textWidth && mouseY >= y && mouseY <= y + textHeight) {
                isDragging = true;
                dragStartX = (int) mouseX;
                dragStartY = (int) mouseY;
                originalX = x;
                originalY = y;
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging && button == 0) {
            int newX = originalX + (int) (mouseX - dragStartX);
            int newY = originalY + (int) (mouseY - dragStartY);

            // Update config
            config.setTitleX(newX);
            config.setTitleY(newY);

            // Update sliders
            updateSliders();

            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0 && isDragging) {
            isDragging = false;
            return true;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public void close() {
        client.setScreen(parent);
    }
}
