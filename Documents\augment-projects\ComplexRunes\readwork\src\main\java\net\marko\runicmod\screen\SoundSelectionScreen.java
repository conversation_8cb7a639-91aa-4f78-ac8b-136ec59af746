package net.marko.runicmod.screen;

import net.marko.runicmod.RunicMod;
import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.config.SoundOptions;
import net.marko.runicmod.sound.SoundManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextWidget;
import net.minecraft.text.Text;
import org.slf4j.Logger;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Screen for selecting sounds
 */
public class SoundSelectionScreen extends Screen {
    private static final Logger LOGGER = RunicMod.LOGGER;

    private final Screen parent;
    private final RunicModConfig config;

    // Widgets
    private ButtonWidget backButton;
    private ButtonWidget importButton;
    private ButtonWidget testButton;

    // Sound list
    private List<ButtonWidget> soundButtons = new ArrayList<>();
    private int scrollOffset = 0;
    private static final int MAX_VISIBLE_SOUNDS = 10;

    // Current view
    private boolean showingMinecraftSounds = true;

    public SoundSelectionScreen(Screen parent) {
        super(Text.literal("Sound Selection"));
        this.parent = parent;
        this.config = RunicModConfig.getInstance();
    }

    @Override
    protected void init() {
        super.init();

        int buttonWidth = 150;
        int buttonHeight = 20;
        int centerX = width / 2;
        int startY = 40;
        int spacing = 24;

        // Add back button
        backButton = ButtonWidget.builder(Text.literal("Back"), button -> {
            client.setScreen(parent);
        }).dimensions(centerX - buttonWidth / 2, height - 30, buttonWidth, buttonHeight).build();
        addDrawableChild(backButton);

        // Add tab buttons
        ButtonWidget minecraftSoundsButton = ButtonWidget.builder(Text.literal("Minecraft Sounds"), button -> {
            showingMinecraftSounds = true;
            scrollOffset = 0;
            updateSoundList();
        }).dimensions(centerX - buttonWidth - 5, startY, buttonWidth, buttonHeight).build();
        addDrawableChild(minecraftSoundsButton);

        ButtonWidget customSoundButton = ButtonWidget.builder(Text.literal("Custom Sounds"), button -> {
            showingMinecraftSounds = false;
            scrollOffset = 0;
            updateSoundList();
        }).dimensions(centerX + 5, startY, buttonWidth, buttonHeight).build();
        addDrawableChild(customSoundButton);

        // Add test sound button
        testButton = ButtonWidget.builder(Text.literal("Test Sound"), button -> {
            SoundManager.playSound(config.getSoundVolume());
        }).dimensions(centerX - buttonWidth / 2, height - 60, buttonWidth, buttonHeight).build();
        addDrawableChild(testButton);

        // Add import button (only visible in custom sounds tab)
        importButton = ButtonWidget.builder(Text.literal("Import Sound File"), button -> {
            importSoundFile();
        }).dimensions(centerX - buttonWidth / 2, height - 90, buttonWidth, buttonHeight).build();

        // Initialize the sound list
        updateSoundList();
    }

    /**
     * Updates the sound list based on the current view
     */
    private void updateSoundList() {
        // Remove existing sound buttons
        for (ButtonWidget button : soundButtons) {
            remove(button);
        }
        soundButtons.clear();

        // Update tab button states
        for (var child : children()) {
            if (child instanceof ButtonWidget button) {
                if (button.getMessage().getString().equals("Minecraft Sounds")) {
                    button.active = !showingMinecraftSounds;
                } else if (button.getMessage().getString().equals("Custom Sounds")) {
                    button.active = showingMinecraftSounds;
                }
            }
        }

        // Show/hide import button
        if (showingMinecraftSounds) {
            remove(importButton);
        } else {
            addDrawableChild(importButton);
        }

        int buttonWidth = 200;
        int buttonHeight = 20;
        int centerX = width / 2;
        int startY = 70;
        int spacing = 22;

        if (showingMinecraftSounds) {
            // Show Minecraft sounds
            List<SoundOptions.SoundOption> sounds = SoundOptions.getMinecraftSounds();

            // Add scroll buttons if needed
            if (sounds.size() > MAX_VISIBLE_SOUNDS) {
                // Add up button
                ButtonWidget upButton = ButtonWidget.builder(Text.literal("↑"), button -> {
                    scrollOffset = Math.max(0, scrollOffset - 1);
                    updateSoundList();
                }).dimensions(centerX + buttonWidth / 2 + 5, startY, 20, 20).build();
                addDrawableChild(upButton);
                soundButtons.add(upButton);

                // Add down button
                ButtonWidget downButton = ButtonWidget.builder(Text.literal("↓"), button -> {
                    scrollOffset = Math.min(sounds.size() - MAX_VISIBLE_SOUNDS, scrollOffset + 1);
                    updateSoundList();
                }).dimensions(centerX + buttonWidth / 2 + 5, startY + spacing * (MAX_VISIBLE_SOUNDS - 1), 20, 20).build();
                addDrawableChild(downButton);
                soundButtons.add(downButton);
            }

            // Add sound buttons
            int count = 0;
            for (SoundOptions.SoundOption sound : sounds) {
                if (count >= scrollOffset && count < scrollOffset + MAX_VISIBLE_SOUNDS) {
                    String soundId = sound.getId();
                    String displayName = sound.getName();

                    ButtonWidget soundButton = ButtonWidget.builder(
                            Text.literal(config.getSelectedSound().equals(soundId) && !config.isUseCustomSound()
                                    ? "✓ " + displayName : displayName),
                            button -> {
                                config.setSelectedSound(soundId);
                                config.setUseCustomSound(false);
                                SoundManager.playSound(config.getSoundVolume());
                                updateSoundList();
                            }).dimensions(centerX - buttonWidth / 2, startY + spacing * (count - scrollOffset), buttonWidth, buttonHeight).build();

                    addDrawableChild(soundButton);
                    soundButtons.add(soundButton);
                }
                count++;
            }
        } else {
            // Show custom sounds
            List<SoundOptions.SoundOption> sounds = SoundOptions.getCustomSounds();

            if (sounds.isEmpty()) {
                // Show message if no custom sounds
                ButtonWidget noSoundsText = ButtonWidget.builder(
                        Text.literal("No custom sounds found. Click 'Import Sound File' to add one."),
                        button -> {})
                        .dimensions(centerX - 200, startY + 30, 400, 20)
                        .build();
                addDrawableChild(noSoundsText);
                soundButtons.add(noSoundsText);
            } else {
                // Add scroll buttons if needed
                if (sounds.size() > MAX_VISIBLE_SOUNDS) {
                    // Add up button
                    ButtonWidget upButton = ButtonWidget.builder(Text.literal("↑"), button -> {
                        scrollOffset = Math.max(0, scrollOffset - 1);
                        updateSoundList();
                    }).dimensions(centerX + buttonWidth / 2 + 5, startY, 20, 20).build();
                    addDrawableChild(upButton);
                    soundButtons.add(upButton);

                    // Add down button
                    ButtonWidget downButton = ButtonWidget.builder(Text.literal("↓"), button -> {
                        scrollOffset = Math.min(sounds.size() - MAX_VISIBLE_SOUNDS, scrollOffset + 1);
                        updateSoundList();
                    }).dimensions(centerX + buttonWidth / 2 + 5, startY + spacing * (MAX_VISIBLE_SOUNDS - 1), 20, 20).build();
                    addDrawableChild(downButton);
                    soundButtons.add(downButton);
                }

                // Add sound buttons
                int count = 0;
                for (SoundOptions.SoundOption sound : sounds) {
                    if (count >= scrollOffset && count < scrollOffset + MAX_VISIBLE_SOUNDS) {
                        String soundId = sound.getId();
                        String displayName = sound.getName();

                        ButtonWidget soundButton = ButtonWidget.builder(
                                Text.literal(config.getSelectedSound().equals(soundId) && config.isUseCustomSound()
                                        ? "✓ " + displayName : displayName),
                                button -> {
                                    config.setSelectedSound(soundId);
                                    config.setUseCustomSound(true);
                                    SoundManager.playSound(config.getSoundVolume());
                                    updateSoundList();
                                }).dimensions(centerX - buttonWidth / 2, startY + spacing * (count - scrollOffset), buttonWidth, buttonHeight).build();

                        addDrawableChild(soundButton);
                        soundButtons.add(soundButton);
                    }
                    count++;
                }
            }
        }
    }

    /**
     * Opens a file chooser to import a sound file
     */
    private void importSoundFile() {
        // This needs to run in a separate thread to avoid blocking the game
        new Thread(() -> {
            try {
                // Create a file chooser
                JFileChooser fileChooser = new JFileChooser();
                fileChooser.setDialogTitle("Select Sound File");
                fileChooser.setFileFilter(new FileNameExtensionFilter("Sound Files", "wav", "mp3", "ogg"));

                // Show the file chooser
                int result = fileChooser.showOpenDialog(null);

                // Process the result
                if (result == JFileChooser.APPROVE_OPTION) {
                    File selectedFile = fileChooser.getSelectedFile();

                    // Import the file
                    String fileName = SoundManager.importSoundFile(selectedFile);

                    // Update the config
                    if (fileName != null) {
                        config.setCustomSoundPath(fileName);
                        config.setUseCustomSound(true);
                        config.setSelectedSound(fileName);

                        // Update the UI on the main thread
                        MinecraftClient.getInstance().execute(() -> {
                            updateSoundList();
                        });
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Failed to import sound file", e);
            }
        }).start();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        renderBackground(context, mouseX, mouseY, delta);

        // Draw title
        context.drawCenteredTextWithShadow(textRenderer, title, width / 2, 10, 0xFFFFFF);

        // Draw current sound info
        String soundInfo = "Current Sound: " + SoundOptions.getDisplayName(config.getSelectedSound());
        context.drawCenteredTextWithShadow(textRenderer, soundInfo, width / 2, height - 45, 0xFFFFFF);

        super.render(context, mouseX, mouseY, delta);
    }



    @Override
    public void close() {
        client.setScreen(parent);
    }
}
