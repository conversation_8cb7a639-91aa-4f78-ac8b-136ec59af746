@echo off
echo Finding Java 21 installation...
echo.

echo Current Java version:
java -version
echo.

echo Searching for Java installations...
echo.

REM Check common Oracle Java paths
if exist "C:\Program Files\Java\jdk-21" (
    echo Found: C:\Program Files\Java\jdk-21
)
if exist "C:\Program Files\Java\jdk-21.0.7" (
    echo Found: C:\Program Files\Java\jdk-21.0.7
)
if exist "C:\Program Files\Java\jdk-21.0.8" (
    echo Found: C:\Program Files\Java\jdk-21.0.8
)

REM Check Eclipse Adoptium paths
if exist "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot" (
    echo Found: C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
)
if exist "C:\Program Files\Eclipse Adoptium\jdk-21" (
    echo Found: C:\Program Files\Eclipse Adoptium\jdk-21
)

REM Check Microsoft OpenJDK paths
if exist "C:\Program Files\Microsoft\jdk-21" (
    echo Found: C:\Program Files\Microsoft\jdk-21
)

REM Check Program Files (x86) for 32-bit installations
if exist "C:\Program Files (x86)\Java\jdk-21" (
    echo Found: C:\Program Files (x86)\Java\jdk-21
)

echo.
echo Checking registry for Java installations...

REM Query registry for Java installations
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\JavaSoft\JDK" /s 2>nul | findstr "JavaHome" | findstr "21\."

echo.
echo Checking where java command points to...
where java

echo.
echo ================================
echo Manual Check Instructions:
echo ================================
echo 1. Look at the paths listed above
echo 2. Find one that contains "21" in the version
echo 3. Use that path in the build command
echo.
echo Example commands to try:
echo set JAVA_HOME=C:\Program Files\Java\jdk-21 ^& gradlew clean build
echo set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot ^& gradlew clean build
echo.

pause
