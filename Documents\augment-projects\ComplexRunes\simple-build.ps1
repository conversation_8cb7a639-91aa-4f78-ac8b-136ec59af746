Write-Host "Building Complex Runes Mod..."

# Set JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Java\jdk-21"
Write-Host "Using JAVA_HOME: $env:JAVA_HOME"

# Clean and build
Write-Host "Cleaning..."
.\gradlew clean

Write-Host "Building..."
.\gradlew build

if ($LASTEXITCODE -eq 0) {
    Write-Host "BUILD SUCCESS!"
    Write-Host "Your mod is ready at: build\libs\complex-runes-1.0.0.jar"
} else {
    Write-Host "BUILD FAILED!"
    Write-Host "Check the error messages above."
}

Read-Host "Press Enter to continue"
