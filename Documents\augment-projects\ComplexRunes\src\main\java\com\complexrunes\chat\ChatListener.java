package com.complexrunes.chat;

import net.fabricmc.fabric.api.client.message.v1.ClientReceiveMessageEvents;
import com.complexrunes.ComplexRunesMod;
import com.complexrunes.config.RuneModConfig;
import com.complexrunes.title.TitleManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;
import org.slf4j.Logger;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.HashMap;
import java.util.Map;

public class ChatListener {
    private static final long MESSAGE_COOLDOWN_MS = 350;
    private static long lastMessageTime = 0;
    private static final Logger LOGGER = ComplexRunesMod.LOGGER;

    // Store the current message being processed
    private static String currentMessage = null;

    // Track active rune notifications with player names and end times
    private static final Map<String, Long> activeRuneNotifications = new HashMap<>();

    public static void register() {
        ClientReceiveMessageEvents.ALLOW_GAME.register((message, overlay) -> {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastMessageTime < MESSAGE_COOLDOWN_MS) return true;

            String msg = message.getString();

            // Store the current message for use in processRuneData
            currentMessage = msg;

            // Log all incoming chat messages at debug level
            LOGGER.debug("Received chat message: {}", msg);

            String playerName = null;
            String duration = null;
            boolean isEndMessage = false;

            // Check if the message matches our rune detection patterns
            if (matchesRunePattern(msg)) {
                try {
                    // Extract rune information from the message
                    RuneInfo runeInfo = extractRuneInfo(msg);
                    if (runeInfo != null) {
                        playerName = runeInfo.playerName;
                        duration = runeInfo.duration;
                        isEndMessage = runeInfo.isEndMessage;

                        if (!isEndMessage && duration != null) {
                            // Check if the duration is valid (1-60 seconds for rune notifications)
                            int durationValue = Integer.parseInt(duration);
                            if (durationValue >= 1 && durationValue <= 60) {
                                processRuneData(playerName, duration);
                            } else {
                                LOGGER.info("Ignoring rune notification with invalid duration: {}", durationValue);
                            }
                        } else if (isEndMessage) {
                            // Remove from active notifications
                            activeRuneNotifications.remove(playerName);
                            LOGGER.info("Rune notification for {} has ended", playerName);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("Error parsing rune message: {}", msg, e);
                }
            }

            return true;
        });
    }

    /**
     * Checks if a message matches any of our rune detection patterns
     */
    private static boolean matchesRunePattern(String message) {
        RuneModConfig config = RuneModConfig.getInstance();
        
        // Check against configured keywords
        for (String keyword : config.getRuneKeywords()) {
            if (config.isCaseSensitive()) {
                if (message.contains(keyword)) {
                    return true;
                }
            } else {
                if (message.toLowerCase().contains(keyword.toLowerCase())) {
                    return true;
                }
            }
        }

        // Check against regex pattern if enabled
        if (config.isUseRegex() && !config.getCustomRegexPattern().isEmpty()) {
            try {
                Pattern pattern = Pattern.compile(config.getCustomRegexPattern(), 
                    config.isCaseSensitive() ? 0 : Pattern.CASE_INSENSITIVE);
                return pattern.matcher(message).find();
            } catch (Exception e) {
                LOGGER.warn("Invalid regex pattern: {}", config.getCustomRegexPattern(), e);
            }
        }

        return false;
    }

    /**
     * Extracts rune information from a message
     */
    private static RuneInfo extractRuneInfo(String message) {
        // Pattern 1: "Player has activated a rune for X seconds"
        Pattern pattern1 = Pattern.compile("([^\\s]+) has activated a rune for (\\d+) seconds?", Pattern.CASE_INSENSITIVE);
        Matcher matcher1 = pattern1.matcher(message);
        if (matcher1.find()) {
            return new RuneInfo(matcher1.group(1), matcher1.group(2), false);
        }

        // Pattern 2: "Rune effect on Player will last X seconds"
        Pattern pattern2 = Pattern.compile("Rune effect on ([^\\s]+) will last (\\d+) seconds?", Pattern.CASE_INSENSITIVE);
        Matcher matcher2 = pattern2.matcher(message);
        if (matcher2.find()) {
            return new RuneInfo(matcher2.group(1), matcher2.group(2), false);
        }

        // Pattern 3: "Player's rune has expired"
        Pattern pattern3 = Pattern.compile("([^\\s]+)'s rune has expired", Pattern.CASE_INSENSITIVE);
        Matcher matcher3 = pattern3.matcher(message);
        if (matcher3.find()) {
            return new RuneInfo(matcher3.group(1), null, true);
        }

        // Pattern 4: Generic rune mention with duration
        Pattern pattern4 = Pattern.compile(".*rune.*?([^\\s]+).*?(\\d+)\\s*seconds?", Pattern.CASE_INSENSITIVE);
        Matcher matcher4 = pattern4.matcher(message);
        if (matcher4.find()) {
            return new RuneInfo(matcher4.group(1), matcher4.group(2), false);
        }

        // If we can't extract specific info, create a generic notification
        // Extract any player name and duration if possible
        Pattern playerPattern = Pattern.compile("([A-Za-z0-9_]{3,16})");
        Pattern durationPattern = Pattern.compile("(\\d+)\\s*seconds?");
        
        Matcher playerMatcher = playerPattern.matcher(message);
        Matcher durationMatcher = durationPattern.matcher(message);
        
        String playerName = playerMatcher.find() ? playerMatcher.group(1) : "Unknown";
        String duration = durationMatcher.find() ? durationMatcher.group(1) : "30"; // Default 30 seconds
        
        return new RuneInfo(playerName, duration, false);
    }

    private static void processRuneData(String playerName, String duration) {
        // Parse the duration
        int durationSeconds = Integer.parseInt(duration);

        // Check if the duration is reasonable (1-60 seconds for rune notifications)
        if (durationSeconds > 60) {
            LOGGER.info("Ignoring rune notification with duration > 60 seconds - Player: {}, Duration: {}s", playerName, durationSeconds);
            return;
        }

        LOGGER.info("Detected rune notification - Player: {}, Duration: {}s", playerName, duration);

        // Store in active notifications for tracking
        long endTime = System.currentTimeMillis() + (durationSeconds * 1000L);
        activeRuneNotifications.put(playerName, endTime);

        // Show the rune notification using our title system
        TitleManager.showRuneNotification(playerName, duration);
    }

    /**
     * Gets the map of active rune notifications
     */
    public static Map<String, Long> getActiveNotifications() {
        return new HashMap<>(activeRuneNotifications);
    }

    /**
     * Cleans up resources when the mod is unloaded
     */
    public static void shutdown() {
        // Clear active notifications
        activeRuneNotifications.clear();
        LOGGER.info("ChatListener shutdown complete");
    }

    /**
     * Data class for rune information
     */
    private static class RuneInfo {
        final String playerName;
        final String duration;
        final boolean isEndMessage;

        RuneInfo(String playerName, String duration, boolean isEndMessage) {
            this.playerName = playerName;
            this.duration = duration;
            this.isEndMessage = isEndMessage;
        }
    }
}
