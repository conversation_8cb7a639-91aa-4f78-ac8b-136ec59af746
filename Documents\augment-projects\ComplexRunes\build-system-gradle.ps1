Write-Host "Building Complex Runes Mod using System Gradle..." -ForegroundColor Green
Write-Host ""

# Set JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Java\jdk-21"
Write-Host "Using JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Green

# Check if system gradle is available
Write-Host "Checking for system Gradle..." -ForegroundColor Yellow
try {
    $gradleVersion = gradle --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ System Gradle found!" -ForegroundColor Green
        Write-Host $gradleVersion
        Write-Host ""
        
        Write-Host "Building with system Gradle..." -ForegroundColor Yellow
        gradle clean build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "================================" -ForegroundColor Green
            Write-Host "✅ BUILD SUCCESS!" -ForegroundColor Green
            Write-Host "================================" -ForegroundColor Green
            Write-Host ""
            Write-Host "Your mod is ready at: build\libs\complex-runes-1.0.0.jar" -ForegroundColor Cyan
        } else {
            Write-Host "❌ Build failed with system Gradle" -ForegroundColor Red
        }
    } else {
        throw "Gradle not found"
    }
} catch {
    Write-Host "❌ System Gradle not found" -ForegroundColor Red
    Write-Host ""
    Write-Host "Solutions:" -ForegroundColor Yellow
    Write-Host "1. Install Gradle: https://gradle.org/install/" -ForegroundColor Cyan
    Write-Host "2. Or run: .\setup-gradle.ps1 to download Gradle wrapper" -ForegroundColor Cyan
    Write-Host "3. Or use Chocolatey: choco install gradle" -ForegroundColor Cyan
    Write-Host "4. Or use Scoop: scoop install gradle" -ForegroundColor Cyan
}

Write-Host ""
Read-Host "Press Enter to continue"
