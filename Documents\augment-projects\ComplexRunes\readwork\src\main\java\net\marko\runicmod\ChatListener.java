package net.marko.runicmod;

import net.fabricmc.fabric.api.client.message.v1.ClientReceiveMessageEvents;
import net.marko.runicmod.networking.RunicNetworking;
import net.marko.runicmod.sound.SoundManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.math.Vec3d;
import org.slf4j.Logger;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.HashMap;
import java.util.Map;

public class ChatListener {
    private static final String PREFIX = "!";
    private static final long MESSAGE_COOLDOWN_MS = 350;
    private static long lastMessageTime = 0;
    private static final Logger LOGGER = RunicMod.LOGGER;

    // Store the current message being processed
    private static String currentMessage = null;

    // Track active runic obstructions with player names and end times
    private static final Map<String, Long> activeRunicObstructions = new HashMap<>();

    public static void register() {
        ClientReceiveMessageEvents.ALLOW_GAME.register((message, overlay) -> {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastMessageTime < MESSAGE_COOLDOWN_MS) return true;

            String msg = message.getString();

            // Store the current message for use in processRunicData
            currentMessage = msg;

            // Log all incoming chat messages at debug level
            LOGGER.debug("Received chat message: {}", msg);

            String playerName = null;
            String duration = null;
            boolean isEndMessage = false;

            // Format 1: Original game message (old format)
            if (msg.contains("rune effects of") && msg.contains("with your Runic Obstruction")) {
                try {
                    // Extract player name
                    Pattern namePattern = Pattern.compile("rune effects of ([^\\s]+)");
                    Matcher nameMatcher = namePattern.matcher(msg);
                    if (nameMatcher.find()) {
                        playerName = nameMatcher.group(1);

                        // Extract duration
                        Pattern durationPattern = Pattern.compile("for (\\d+) sec");
                        Matcher durationMatcher = durationPattern.matcher(msg);
                        if (durationMatcher.find()) {
                            duration = durationMatcher.group(1);
                            // Process the runic data directly
                            processRunicData(playerName, duration);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("Error parsing runic message: {}", msg, e);
                }
            }
            // Format 2: New format - You disabled someone's runes
            else if (msg.contains("RUNES") && msg.contains("You have disabled the rune effects of") &&
                     msg.contains("with your Runic Obstruction") && msg.contains("seconds")) {
                try {
                    // Use a more robust pattern that ignores clan tags and other prefixes
                    Pattern pattern = Pattern.compile("RUNES.*You have disabled the rune effects of (.*?) with your Runic Obstruction ([IVX]+) for (\\d+) seconds");
                    Matcher matcher = pattern.matcher(msg);
                    if (matcher.find()) {
                        playerName = matcher.group(1);
                        String level = matcher.group(2);
                        duration = matcher.group(3);

                        // Check if the level is between I and VI
                        if (isValidRunicLevel(level)) {
                            // Check if the duration is between 1 and 6 seconds
                            int durationValue = Integer.parseInt(duration);
                            if (durationValue >= 1 && durationValue <= 6) {
                                // Process the runic data directly
                                processRunicData(playerName, duration);
                            } else {
                                LOGGER.info("Ignoring runic obstruction with invalid duration: {}", durationValue);
                            }
                        } else {
                            LOGGER.debug("Ignoring runic obstruction with invalid level: {}", level);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("Error parsing runic message: {}", msg, e);
                }
            }
            // Format 3: New format - Someone disabled your runes
            else if (msg.contains("RUNES") && msg.contains("has prevented your custom enchants from working") &&
                     msg.contains("with their Runic Obstruction")) {
                try {
                    // Use a more robust pattern that ignores clan tags and other prefixes
                    Pattern pattern = Pattern.compile("RUNES.*?([^\\s]+) has prevented your custom enchants from working for (\\d+) seconds");
                    Matcher matcher = pattern.matcher(msg);
                    if (matcher.find()) {
                        // This is when someone else used Runic Obstruction on you
                        // We don't need to send a message in this case, just log it
                        String caster = matcher.group(1);
                        duration = matcher.group(2);
                        LOGGER.info("Your runes were disabled by {} for {}s", caster, duration);
                    }
                } catch (Exception e) {
                    LOGGER.error("Error parsing runic message: {}", msg, e);
                }
            }
            // Format 4: End message - Your obstruction wore off
            else if (msg.contains("RUNES") && msg.contains("The effect of your Runic Obstruction") &&
                     msg.contains("has worn off")) {
                try {
                    // Use a more robust pattern that ignores clan tags and other prefixes
                    Pattern pattern = Pattern.compile("RUNES.*The effect of your Runic Obstruction on ([^\\s]+) has worn off");
                    Matcher matcher = pattern.matcher(msg);
                    if (matcher.find()) {
                        playerName = matcher.group(1);
                        isEndMessage = true;

                        // Remove from active obstructions
                        activeRunicObstructions.remove(playerName);

                        // Remove glow effect
                        GlowManager.removeGlowingPlayer(playerName);

                        LOGGER.info("Runic obstruction on {} has ended", playerName);
                    }
                } catch (Exception e) {
                    LOGGER.error("Error parsing runic message: {}", msg, e);
                }
            }
            // Format 5: Mod communication format - "! (name) has been runiced for (duration)"
            else if (msg.contains("has been runiced for")) {
                try {
                    // Check if this is our own message with the prefix
                    if (msg.startsWith(PREFIX) && System.currentTimeMillis() - lastMessageTime < 500) {
                        // This is likely our own message, ignore it
                        LOGGER.debug("Ignoring our own message: {}", msg);
                        return true;
                    }

                    // Extract player name and duration - look for the format without the prefix
                    // This allows us to detect both our messages (with prefix) and other messages
                    Pattern pattern = Pattern.compile("(?:" + PREFIX + "\\s+)?([^\\s]+)\\s+has been runiced for\\s+(\\d+)");
                    Matcher matcher = pattern.matcher(msg);
                    if (matcher.find()) {
                        playerName = matcher.group(1);
                        duration = matcher.group(2);

                        // Check if the duration is between 1 and 6 seconds
                        int durationValue = Integer.parseInt(duration);
                        if (durationValue >= 1 && durationValue <= 6) {
                            // Process the runic data
                            processRunicData(playerName, duration);
                        } else {
                            LOGGER.info("Ignoring runic message with invalid duration: {}", durationValue);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("Error parsing mod communication message: {}", msg, e);
                }
            }

            if (playerName != null) {
                if (!isEndMessage && duration != null) {
                    // Check if the duration is 6 seconds or less
                    try {
                        int durationValue = Integer.parseInt(duration);
                        if (durationValue >= 1 && durationValue <= 6) {
                            processRunicData(playerName, duration);
                        } else {
                            LOGGER.info("Ignoring runic message with invalid duration: {}", durationValue);
                        }
                    } catch (NumberFormatException e) {
                        LOGGER.error("Error parsing duration: {}", duration, e);
                    }
                }
            }
            return true;
        });
    }

    /**
     * Sends a formatted chat message to communicate runic status to other clients with the mod
     */
    private static void sendServerChatMessage(String playerName, String duration) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null || client.getNetworkHandler() == null) return;

        // Check if we've recently sent a message to avoid duplicates
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastMessageTime < MESSAGE_COOLDOWN_MS) {
            LOGGER.debug("Skipping message due to cooldown");
            return;
        }

        // Format: "! (name) has been runiced for (duration)"
        String message = PREFIX + " " + playerName + " has been runiced for " + duration;
        lastMessageTime = currentTime;
        client.getNetworkHandler().sendChatMessage(message);
        LOGGER.debug("Sent runic status message: {}", message);
    }

    private static void processRunicData(String playerName, String duration) {
        // Parse the duration
        int durationSeconds = Integer.parseInt(duration);

        // Check if the duration is 6 seconds or less
        if (durationSeconds > 6) {
            LOGGER.info("Ignoring runic obstruction with duration > 6 seconds - Player: {}, Duration: {}s", playerName, durationSeconds);
            return;
        }

        LOGGER.info("Detected runic obstruction - Player: {}, Duration: {}s", playerName, duration);

        // Store in active obstructions for tracking
        long endTime = System.currentTimeMillis() + (durationSeconds * 1000L);
        activeRunicObstructions.put(playerName, endTime);

        // Send a chat message to communicate with other clients
        // This is only done for original game messages, not for our own formatted messages
        if (currentMessage != null && currentMessage.contains("RUNES") && !currentMessage.contains("has been runiced for")) {
            sendServerChatMessage(playerName, duration);
        }

        // Get max distance from config
        int maxDistance = net.marko.runicmod.config.RunicModConfig.getInstance().getMaxDistance();

        // Check if the player is within the configured distance before applying visual effects
        if (isPlayerWithinRange(playerName, maxDistance)) {
            // Play the ding sound only if player is within range and sounds are enabled
            if (net.marko.runicmod.config.RunicModConfig.getInstance().isPlaySounds()) {
                playDingSound(net.marko.runicmod.config.RunicModConfig.getInstance().getSoundVolume());
            }

            // Apply glow effect to the player using our GlowManager if glow is enabled
            if (net.marko.runicmod.config.RunicModConfig.getInstance().isShowGlow()) {
                GlowManager.addGlowingPlayer(playerName, durationSeconds);
                LOGGER.debug("Applied glow effect to player {}", playerName);
            }

            // Show title in the middle of the screen if title is enabled
            if (net.marko.runicmod.config.RunicModConfig.getInstance().isShowTitle()) {
                TitleManager.showRunicTitle(playerName, duration);
                LOGGER.debug("Showed title for player {}", playerName);
            }

            LOGGER.info("Applied visual effects for player {} (within range)", playerName);
        } else {
            LOGGER.info("Player {} is too far away, not applying visual effects", playerName);
        }
    }

    /**
     * Plays the configured sound
     * @param volume The volume of the sound (0.0 to 1.0)
     */
    private static void playDingSound(float volume) {
        // Use the SoundManager to play the configured sound
        SoundManager.playSound(volume);
    }

    /**
     * Checks if a runic level is valid (between I and VI)
     * @param level The runic level in Roman numerals
     * @return true if the level is valid, false otherwise
     */
    private static boolean isValidRunicLevel(String level) {
        switch (level) {
            case "I":
            case "II":
            case "III":
            case "IV":
            case "V":
            case "VI":
                return true;
            default:
                return false;
        }
    }

    /**
     * Checks if a player is within the specified range of the client player
     * @param playerName The name of the player to check
     * @param range The maximum distance in blocks
     * @return true if the player is within range, false otherwise
     */
    private static boolean isPlayerWithinRange(String playerName, double range) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null || client.world == null) return false;

        // Get the client player's position
        Vec3d clientPos = client.player.getPos();

        // Find the target player
        for (PlayerEntity player : client.world.getPlayers()) {
            if (player.getName().getString().equals(playerName)) {
                // Calculate distance
                Vec3d playerPos = player.getPos();
                double distance = clientPos.distanceTo(playerPos);

                LOGGER.debug("Distance to player {}: {} blocks", playerName, distance);

                // Check if within range
                return distance <= range;
            }
        }

        // Player not found in the world
        LOGGER.debug("Player {} not found in the world", playerName);
        return false;
    }



    /**
     * Cleans up resources when the mod is unloaded
     */
    public static void shutdown() {
        // Clear active obstructions
        activeRunicObstructions.clear();
        LOGGER.info("ChatListener shutdown complete");
    }
}