Write-Host "Building Complex Runes Mod with Updated Gradle..." -ForegroundColor Green
Write-Host ""

# Set JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Java\jdk-21"
Write-Host "Using JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Green
Write-Host ""

Write-Host "Note: First build will download Gradle 8.12 (this may take a few minutes)" -ForegroundColor Yellow
Write-Host ""

Write-Host "Cleaning previous build..." -ForegroundColor Yellow
.\gradlew clean

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Clean successful" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Building mod..." -ForegroundColor Yellow
    .\gradlew build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "================================" -ForegroundColor Green
        Write-Host "✅ BUILD SUCCESS!" -ForegroundColor Green
        Write-Host "================================" -ForegroundColor Green
        Write-Host ""
        
        # Check if the jar file exists
        $jarFile = "build\libs\complex-runes-1.0.0.jar"
        if (Test-Path $jarFile) {
            $fileSize = (Get-Item $jarFile).Length
            Write-Host "Your mod is ready!" -ForegroundColor Cyan
            Write-Host "File: $jarFile" -ForegroundColor Cyan
            Write-Host "Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "Installation:" -ForegroundColor Yellow
            Write-Host "1. Install Fabric Loader 0.16.14+ for Minecraft 1.21.1" -ForegroundColor White
            Write-Host "2. Install Fabric API 0.116.0+1.21.1" -ForegroundColor White
            Write-Host "3. Install Cloth Config 15.0.140+" -ForegroundColor White
            Write-Host "4. Copy $jarFile to your .minecraft\mods\ folder" -ForegroundColor White
            Write-Host ""
            Write-Host "To test in development: .\gradlew runClient" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️ Build succeeded but JAR file not found at expected location" -ForegroundColor Yellow
            Write-Host "Check build\libs\ directory for output files" -ForegroundColor Yellow
        }
    } else {
        Write-Host ""
        Write-Host "================================" -ForegroundColor Red
        Write-Host "❌ BUILD FAILED!" -ForegroundColor Red
        Write-Host "================================" -ForegroundColor Red
        Write-Host "Check the error messages above for details." -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Clean failed" -ForegroundColor Red
    Write-Host "Check the error messages above for details." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to continue"
