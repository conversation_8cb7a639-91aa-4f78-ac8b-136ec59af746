package net.marko.runicmod.config;

import net.marko.runicmod.RunicMod;
import net.marko.runicmod.sound.SoundManager;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manages sound options for the Runic Mod
 */
public class SoundOptions {
    private static final Logger LOGGER = RunicMod.LOGGER;

    // List of available Minecraft sounds
    private static final List<SoundOption> MINECRAFT_SOUNDS = new ArrayList<>();

    // Initialize the sound list
    static {
        // Add common Minecraft sounds
        addSound("Experience Orb Pickup", "minecraft:entity.experience_orb.pickup");
        addSound("Player Level Up", "minecraft:entity.player.levelup");
        addSound("Note Block Pling", "minecraft:block.note_block.pling");
        addSound("Anvil Land", "minecraft:block.anvil.land");
        addSound("Arrow Hit Player", "minecraft:entity.arrow.hit_player");
        addSound("Player Hurt", "minecraft:entity.player.hurt");
        addSound("Enderman Teleport", "minecraft:entity.enderman.teleport");
        addSound("Bell Ring", "minecraft:block.bell.use");
        addSound("Trident Thunder", "minecraft:item.trident.thunder");
        addSound("Lightning Thunder", "minecraft:entity.lightning_bolt.thunder");
        addSound("Villager Yes", "minecraft:entity.villager.yes");
        addSound("Villager No", "minecraft:entity.villager.no");
        addSound("Totem Use", "minecraft:item.totem.use");
        addSound("Ghast Warn", "minecraft:entity.ghast.warn");
        addSound("Blaze Shoot", "minecraft:entity.blaze.shoot");
    }

    /**
     * Adds a sound to the list
     * @param name The display name of the sound
     * @param soundId The sound ID
     */
    private static void addSound(String name, String soundId) {
        MINECRAFT_SOUNDS.add(new SoundOption(name, soundId));
    }

    /**
     * Gets the list of available Minecraft sounds
     * @return The list of sounds
     */
    public static List<SoundOption> getMinecraftSounds() {
        return MINECRAFT_SOUNDS;
    }

    /**
     * Gets the list of available custom sounds
     * @return The list of custom sounds
     */
    public static List<SoundOption> getCustomSounds() {
        List<SoundOption> customSounds = new ArrayList<>();

        // Add custom sounds from the sounds directory
        SoundManager.getCustomSoundFiles().forEach(file -> {
            customSounds.add(new SoundOption(file.getName(), file.getName()));
        });

        return customSounds;
    }

    /**
     * Gets the display name for a sound ID
     * @param soundId The sound ID
     * @return The display name
     */
    public static String getDisplayName(String soundId) {
        // Check Minecraft sounds
        for (SoundOption option : MINECRAFT_SOUNDS) {
            if (option.getId().equals(soundId)) {
                return option.getName();
            }
        }

        // Check custom sounds
        for (SoundOption option : getCustomSounds()) {
            if (option.getId().equals(soundId)) {
                return "Custom: " + option.getName();
            }
        }

        return soundId;
    }

    /**
     * Represents a sound option
     */
    public static class SoundOption {
        private final String name;
        private final String id;

        public SoundOption(String name, String id) {
            this.name = name;
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public String getId() {
            return id;
        }
    }
}
