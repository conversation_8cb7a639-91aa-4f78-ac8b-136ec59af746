package net.marko.runicmod.config;

import com.terraformersmc.modmenu.api.ConfigScreenFactory;
import com.terraformersmc.modmenu.api.ModMenuApi;
import me.shedaniel.clothconfig2.api.AbstractConfigListEntry;
import me.shedaniel.clothconfig2.api.ConfigBuilder;
import me.shedaniel.clothconfig2.api.ConfigCategory;
import me.shedaniel.clothconfig2.api.ConfigEntryBuilder;
import net.marko.runicmod.screen.SoundSelectionScreen;
import net.marko.runicmod.screen.TextPositionScreen;
import net.marko.runicmod.sound.SoundManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

import javax.swing.JFileChooser;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import com.google.common.collect.Lists;

/**
 * Integration with Mod Menu to provide a configuration screen
 */
public class RunicModMenuIntegration implements ModMenuApi {
    @Override
    public ConfigScreenFactory<?> getModConfigScreenFactory() {
        return this::createConfigScreen;
    }

    /**
     * Creates the configuration screen
     */
    private Screen createConfigScreen(Screen parent) {
        RunicModConfig config = RunicModConfig.getInstance();

        ConfigBuilder builder = ConfigBuilder.create()
                .setParentScreen(parent)
                .setTitle(Text.literal("Runic Mod Configuration"));

        ConfigEntryBuilder entryBuilder = builder.entryBuilder();

        // Display category
        ConfigCategory displayCategory = builder.getOrCreateCategory(Text.literal("Display"));

        // Add X position field
        displayCategory.addEntry(entryBuilder.startIntField(
                Text.literal("X Position"),
                config.getTitleX() == -1 ? 400 : config.getTitleX())
                .setDefaultValue(-1)
                .setTooltip(Text.literal("Horizontal position of the title text"))
                .setSaveConsumer(value -> {
                    config.setTitleX(value);
                })
                .build());

        // Add Y position field
        displayCategory.addEntry(entryBuilder.startIntField(
                Text.literal("Y Position"),
                config.getTitleY())
                .setDefaultValue(20)
                .setTooltip(Text.literal("Vertical position of the title text"))
                .setSaveConsumer(config::setTitleY)
                .build());

        // Add center horizontally toggle
        displayCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Center Horizontally"),
                config.getTitleX() == -1)
                .setDefaultValue(true)
                .setTooltip(Text.literal("Center the text horizontally"))
                .setSaveConsumer(value -> {
                    if (value) {
                        config.setTitleX(-1);
                    } else if (config.getTitleX() == -1) {
                        // If it was centered before, set it to the middle
                        config.setTitleX(400);
                    }
                })
                .build());

        // Add scale slider
        displayCategory.addEntry(entryBuilder.startIntSlider(
                Text.literal("Text Scale (%)"),
                (int) (config.getTitleScale() * 100),
                50, 200)
                .setDefaultValue(100)
                .setTooltip(Text.literal("Scale of the title text (in percent)"))
                .setSaveConsumer(value -> config.setTitleScale(value / 100.0f))
                .build());

        // Create a list of color options for the dropdown
        List<String> colorOptions = new ArrayList<>();
        for (ColorOption option : ColorOption.values()) {
            colorOptions.add(option.getName());
        }

        // Add title color dropdown
        displayCategory.addEntry(entryBuilder.startDropdownMenu(
                Text.literal("Title Color"),
                ColorOption.fromName(config.getTitleColorOption()).getName(),
                option -> option)
                .setSelections(colorOptions)
                .setDefaultValue(ColorOption.RED.getName())
                .setTooltip(Text.literal("Color of the title text"))
                .setSaveConsumer(value -> {
                    ColorOption option = ColorOption.fromName(value);
                    config.setTitleColorOption(option.name());
                })
                .build());

        // Add use custom title color toggle
        displayCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Use Custom Title Color"),
                config.isUseCustomTitleColor())
                .setDefaultValue(false)
                .setTooltip(Text.literal("Use a custom color for the title text instead of a predefined color"))
                .setSaveConsumer(config::setUseCustomTitleColor)
                .build());

        // Add custom title color field
        displayCategory.addEntry(entryBuilder.startColorField(
                Text.literal("Custom Title Color"),
                config.getCustomTitleColor())
                .setDefaultValue(0xFF0000)
                .setTooltip(Text.literal("Custom color for the title text"))
                .setSaveConsumer(config::setCustomTitleColor)
                .build());

        // Add show title toggle
        displayCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Show Title"),
                config.isShowTitle())
                .setDefaultValue(true)
                .setTooltip(Text.literal("Show title text when runic obstruction is detected"))
                .setSaveConsumer(config::setShowTitle)
                .build());

        // Add glow color dropdown
        displayCategory.addEntry(entryBuilder.startDropdownMenu(
                Text.literal("Glow Color"),
                ColorOption.fromName(config.getGlowColorOption()).getName(),
                option -> option)
                .setSelections(colorOptions)
                .setDefaultValue(ColorOption.RED.getName())
                .setTooltip(Text.literal("Color of the glow effect"))
                .setSaveConsumer(value -> {
                    ColorOption option = ColorOption.fromName(value);
                    config.setGlowColorOption(option.name());
                })
                .build());

        // Add use custom glow color toggle
        displayCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Use Custom Glow Color"),
                config.isUseCustomGlowColor())
                .setDefaultValue(false)
                .setTooltip(Text.literal("Use a custom color for the glow effect instead of a predefined color"))
                .setSaveConsumer(config::setUseCustomGlowColor)
                .build());

        // Add custom glow color field
        displayCategory.addEntry(entryBuilder.startColorField(
                Text.literal("Custom Glow Color"),
                config.getCustomGlowColor())
                .setDefaultValue(0xFF0000)
                .setTooltip(Text.literal("Custom color for the glow effect"))
                .setSaveConsumer(config::setCustomGlowColor)
                .build());

        // Add show glow toggle
        displayCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Show Glow"),
                config.isShowGlow())
                .setDefaultValue(true)
                .setTooltip(Text.literal("Show glow effect when runic obstruction is detected"))
                .setSaveConsumer(config::setShowGlow)
                .build());

        // Add max distance slider
        displayCategory.addEntry(entryBuilder.startIntSlider(
                Text.literal("Max Distance"),
                config.getMaxDistance(),
                5, 50)
                .setDefaultValue(25)
                .setTooltip(Text.literal("Maximum distance to show effects (in blocks)"))
                .setSaveConsumer(config::setMaxDistance)
                .build());

        // Sound category
        ConfigCategory soundCategory = builder.getOrCreateCategory(Text.literal("Sound"));

        soundCategory.addEntry(entryBuilder.startBooleanToggle(
                Text.literal("Play Sounds"),
                config.isPlaySounds())
                .setDefaultValue(true)
                .setTooltip(Text.literal("Whether to play sounds when runic obstruction is detected"))
                .setSaveConsumer(config::setPlaySounds)
                .build());

        soundCategory.addEntry(entryBuilder.startFloatField(
                Text.literal("Sound Volume"),
                config.getSoundVolume())
                .setDefaultValue(1.0f)
                .setTooltip(Text.literal("Volume of the sound effects"))
                .setSaveConsumer(config::setSoundVolume)
                .build());

        // Add current sound info
        soundCategory.addEntry(entryBuilder.startTextDescription(Text.literal(""))
                .build());

        soundCategory.addEntry(entryBuilder.startTextDescription(Text.literal("Current Sound: " +
                SoundOptions.getDisplayName(config.getSelectedSound())))
                .build());

        soundCategory.addEntry(entryBuilder.startTextDescription(Text.literal(""))
                .build());

        // Create a subcategory for sound selection
        List<SoundOptions.SoundOption> minecraftSounds = SoundOptions.getMinecraftSounds();

        // Create a list of entries for the sound selection subcategory
        List<AbstractConfigListEntry> soundEntries = new ArrayList<>();

        // Add a label for Minecraft sounds
        soundEntries.add(entryBuilder.startTextDescription(Text.literal("Minecraft Sounds"))
                .build());

        // Add sound selection as a series of toggles
        for (SoundOptions.SoundOption option : minecraftSounds) {
            soundEntries.add(entryBuilder.startBooleanToggle(
                    Text.literal(option.getName()),
                    !config.isUseCustomSound() && config.getSelectedSound().equals(option.getId()))
                    .setDefaultValue(false)
                    .setTooltip(Text.literal("Use this sound"))
                    .setSaveConsumer(value -> {
                        if (value) {
                            config.setSelectedSound(option.getId());
                            config.setUseCustomSound(false);
                        }
                    })
                    .build());
        }

        // Add a label for custom sounds
        soundEntries.add(entryBuilder.startTextDescription(Text.literal(""))
                .build());

        soundEntries.add(entryBuilder.startTextDescription(Text.literal("Custom Sounds"))
                .build());

        // Add custom sound toggle
        soundEntries.add(entryBuilder.startBooleanToggle(
                Text.literal("Use Custom Sound"),
                config.isUseCustomSound())
                .setDefaultValue(false)
                .setTooltip(Text.literal("Use a custom sound file instead of a Minecraft sound"))
                .setSaveConsumer(config::setUseCustomSound)
                .build());

        // Add custom sound path field
        soundEntries.add(entryBuilder.startStrField(
                Text.literal("Custom Sound Path"),
                config.getCustomSoundPath())
                .setDefaultValue("")
                .setTooltip(Text.literal("Path to the custom sound file"))
                .setSaveConsumer(config::setCustomSoundPath)
                .build());

        // Add import button
        soundEntries.add(entryBuilder.startBooleanToggle(
                Text.literal("Import Custom Sound"),
                false)
                .setDefaultValue(false)
                .setTooltip(Text.literal("Import a custom sound file"))
                .setSaveConsumer(value -> {
                    if (value) {
                        // Open a file chooser in a separate thread
                        new Thread(() -> {
                            try {
                                JFileChooser fileChooser = new JFileChooser();
                                fileChooser.setDialogTitle("Select Sound File");
                                fileChooser.setFileFilter(new FileNameExtensionFilter("Sound Files", "wav", "mp3", "ogg"));

                                int result = fileChooser.showOpenDialog(null);
                                if (result == JFileChooser.APPROVE_OPTION) {
                                    File selectedFile = fileChooser.getSelectedFile();
                                    String fileName = SoundManager.importSoundFile(selectedFile);

                                    if (fileName != null) {
                                        config.setCustomSoundPath(fileName);
                                        config.setUseCustomSound(true);
                                        config.setSelectedSound(fileName);
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }).start();
                    }
                })
                .build());

        // Add a label for testing
        soundEntries.add(entryBuilder.startTextDescription(Text.literal(""))
                .build());

        soundEntries.add(entryBuilder.startTextDescription(Text.literal("Testing"))
                .build());

        // Add test sound button
        soundEntries.add(entryBuilder.startBooleanToggle(
                Text.literal("Test Sound"),
                false)
                .setDefaultValue(false)
                .setTooltip(Text.literal("Play the currently selected sound"))
                .setSaveConsumer(value -> {
                    if (value) {
                        SoundManager.playSound(config.getSoundVolume());
                    }
                })
                .build());

        // Add the sound selection subcategory
        soundCategory.addEntry(entryBuilder.startSubCategory(
                Text.literal("Sound Selection"),
                soundEntries)
                .build());

        // Build the screen
        return builder.build();
    }
}
