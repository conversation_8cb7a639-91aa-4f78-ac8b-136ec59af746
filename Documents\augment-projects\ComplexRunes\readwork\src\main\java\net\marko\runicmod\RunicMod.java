package net.marko.runicmod;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.networking.RunicNetworking;
import net.marko.runicmod.render.CustomTextRenderer;
import net.marko.runicmod.render.DraggableText;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RunicMod implements ClientModInitializer {
	public static final Logger LOGGER = LoggerFactory.getLogger("RunicMod");

	// Set to true to enable debug features like sending chat messages
	public static final boolean DEBUG_MODE = false;

	@Override
	public void onInitializeClient() {
		LOGGER.info("Initializing Runic Mod...");

		// Load config
        RunicModConfig.getInstance();

		// Register the chat listener
		ChatListener.register();

		// Register networking
		RunicNetworking.registerNetworking();

		// Register tick event for the GlowManager
		ClientTickEvents.END_CLIENT_TICK.register(client -> {
			GlowManager.tick();
		});

		// Register HUD renderer for custom text
        HudRenderCallback.EVENT.register((drawContext, tickDelta) -> {
            CustomTextRenderer.renderCountdowns(drawContext);
        });

		// Register shutdown hook
		ClientLifecycleEvents.CLIENT_STOPPING.register(client -> {
			LOGGER.info("Shutting down Runic Mod...");
			ChatListener.shutdown();
			GlowManager.shutdown();
			TitleManager.shutdown();
		});

		LOGGER.info("Runic Mod initialized!");
	}
}