package net.marko.runicmod.networking;

import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents;
import net.minecraft.client.MinecraftClient;
import net.marko.runicmod.GlowManager;
import net.marko.runicmod.RunicMod;
import net.marko.runicmod.TitleManager;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * Handles client-to-client communication for the Runic Mod
 *
 * Since we can't use direct networking between clients without a server component,
 * we'll use a simpler approach: store the runic status locally and share it when needed.
 */
public class RunicNetworking {
    private static final Logger LOGGER = RunicMod.LOGGER;

    // Map to store runic status information
    private static final Map<String, Long> runicPlayers = new HashMap<>();

    /**
     * Registers all network handlers
     */
    public static void registerNetworking() {
        LOGGER.info("Registering Runic Mod networking");

        // Register connection events
        ClientLifecycleEvents.CLIENT_STOPPING.register(client -> {
            clearRunicStatus();
        });
    }

    /**
     * Sends a runic status update to other clients with the mod
     * @param playerName The name of the player affected by runic obstruction
     * @param duration The duration of the runic obstruction in seconds
     */
    public static void sendRunicStatusPacket(String playerName, int duration) {
        // Store the runic status locally
        long expirationTime = System.currentTimeMillis() + (duration * 1000L);
        runicPlayers.put(playerName, expirationTime);

        LOGGER.info("Stored runic status for player: {}, duration: {}", playerName, duration);

        // In a real implementation, we would send a packet to other clients here
        // Since we can't do that without a server component, we'll just store it locally
        // and other clients with the mod would do the same when they detect the chat message
    }

    /**
     * Checks if a player is currently runiced
     * @param playerName The name of the player to check
     * @return true if the player is runiced, false otherwise
     */
    public static boolean isPlayerRuniced(String playerName) {
        Long expirationTime = runicPlayers.get(playerName);
        if (expirationTime == null) {
            return false;
        }

        // Check if the runic status has expired
        if (System.currentTimeMillis() > expirationTime) {
            runicPlayers.remove(playerName);
            return false;
        }

        return true;
    }

    /**
     * Gets the remaining duration of a player's runic status in seconds
     * @param playerName The name of the player to check
     * @return The remaining duration in seconds, or 0 if the player is not runiced
     */
    public static int getRemainingDuration(String playerName) {
        Long expirationTime = runicPlayers.get(playerName);
        if (expirationTime == null) {
            return 0;
        }

        // Calculate the remaining time
        long remainingTime = expirationTime - System.currentTimeMillis();
        if (remainingTime <= 0) {
            runicPlayers.remove(playerName);
            return 0;
        }

        return (int) (remainingTime / 1000);
    }

    /**
     * Clears all runic status information
     */
    public static void clearRunicStatus() {
        runicPlayers.clear();
        LOGGER.info("Cleared all runic status information");
    }
}
