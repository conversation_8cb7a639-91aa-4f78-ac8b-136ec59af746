package net.marko.runicmod;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.math.Vec3d;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manages client-side glowing effects for players
 */
public class GlowManager {
    private static final Logger LOGGER = RunicMod.LOGGER;
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    // Map of player names to expiration times
    private static final Map<String, Long> glowingPlayers = new HashMap<>();

    // Set of entity IDs that should be glowing
    private static final Set<Integer> glowingEntityIds = new HashSet<>();

    /**
     * Adds a player to the glowing players list
     * @param playerName The name of the player to make glow
     * @param durationSeconds How long the player should glow for (in seconds)
     */
    public static void addGlowingPlayer(String playerName, int durationSeconds) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.world == null) return;

        // Calculate expiration time
        long expirationTime = System.currentTimeMillis() + (durationSeconds * 1000L);

        // Store the player in our map
        glowingPlayers.put(playerName, expirationTime);

        LOGGER.info("Added {} to glowing players list for {} seconds", playerName, durationSeconds);

        // Find the player entity and store its ID
        findAndStorePlayerEntityId(playerName);

        // No longer sending chat notifications
    }

    /**
     * Removes a player from the glowing players list
     * @param playerName The name of the player to stop glowing
     */
    public static void removeGlowingPlayer(String playerName) {
        // Remove from glowing players map
        glowingPlayers.remove(playerName);

        // Remove any entity IDs associated with this player
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.world != null) {
            client.world.getPlayers().forEach(player -> {
                if (player.getName().getString().equals(playerName)) {
                    int entityId = player.getId();
                    glowingEntityIds.remove(entityId);
                    LOGGER.debug("Removed entity ID {} for player {}", entityId, playerName);
                }
            });
        }

        // Force a refresh of the entity rendering
        if (client.world != null) {
            client.world.getPlayers().forEach(player -> {
                if (player.getName().getString().equals(playerName)) {
                    // This will force the client to refresh the entity rendering
                    player.setGlowing(false);
                }
            });
        }

        LOGGER.info("Removed {} from glowing players list", playerName);
    }

    /**
     * Clears all glowing players
     */
    public static void clearAllGlowingPlayers() {
        glowingPlayers.clear();
        glowingEntityIds.clear();
        LOGGER.info("Cleared all glowing players");
    }

    /**
     * Checks if an entity should be glowing
     * @param entity The entity to check
     * @return true if the entity should be glowing, false otherwise
     */
    public static boolean shouldGlow(Entity entity) {
        // Check if the entity ID is in our glowing set
        if (glowingEntityIds.contains(entity.getId())) {
            // Check if the entity is a player
            if (entity instanceof PlayerEntity player) {
                // Get the client player
                MinecraftClient client = MinecraftClient.getInstance();
                if (client.player != null) {
                    // Calculate distance to the player
                    double distance = client.player.getPos().distanceTo(entity.getPos());

                    // Only glow if within the configured max distance
                    int maxDistance = net.marko.runicmod.config.RunicModConfig.getInstance().getMaxDistance();
                    return distance <= maxDistance;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * Gets the glow color for an entity
     * @param entity The entity to get the glow color for
     * @return The glow color as a Vec3d (RGB values from 0-1)
     */
    public static Vec3d getGlowColor(Entity entity) {
        // Get the color from config
        int color = net.marko.runicmod.config.RunicModConfig.getInstance().getGlowColor();

        // Convert RGB int to Vec3d (0-1 range)
        double r = ((color >> 16) & 0xFF) / 255.0;
        double g = ((color >> 8) & 0xFF) / 255.0;
        double b = (color & 0xFF) / 255.0;

        return new Vec3d(r, g, b);
    }

    /**
     * Finds a player entity by name and stores its ID in the glowing entity IDs set
     * @param playerName The name of the player to find
     */
    private static void findAndStorePlayerEntityId(String playerName) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.world == null) return;

        client.world.getPlayers().forEach(player -> {
            if (player.getName().getString().equals(playerName)) {
                glowingEntityIds.add(player.getId());
                LOGGER.debug("Added entity ID {} for player {}", player.getId(), playerName);
            }
        });
    }

    /**
     * Updates the glowing status of all players
     * Should be called every tick
     */
    public static void tick() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.world == null) return;

        long currentTime = System.currentTimeMillis();

        // Check for expired players
        Set<String> playersToRemove = new HashSet<>();

        for (Map.Entry<String, Long> entry : glowingPlayers.entrySet()) {
            String playerName = entry.getKey();
            long expirationTime = entry.getValue();

            if (currentTime > expirationTime) {
                // This player's glow effect has expired
                playersToRemove.add(playerName);
                LOGGER.debug("Glow effect for {} has expired", playerName);
            } else {
                // Make sure we have the entity ID for this player
                findAndStorePlayerEntityId(playerName);
            }
        }

        // Remove expired players
        for (String playerName : playersToRemove) {
            removeGlowingPlayer(playerName);
        }
    }



    /**
     * Shuts down the scheduler
     */
    public static void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // Clear all collections
        glowingPlayers.clear();
        glowingEntityIds.clear();

        LOGGER.info("GlowManager shutdown complete");
    }
}
