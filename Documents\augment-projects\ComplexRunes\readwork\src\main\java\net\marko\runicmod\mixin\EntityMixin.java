package net.marko.runicmod.mixin;

import net.marko.runicmod.GlowManager;
import net.marko.runicmod.config.RunicModConfig;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.scoreboard.AbstractTeam;
import net.minecraft.scoreboard.Team;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

/**
 * Mixin to handle custom team colors for glowing entities
 */
@Mixin(Entity.class)
public class EntityMixin {

    /**
     * Injects into the getTeamColorValue method to apply custom glow colors
     */
    @Inject(method = "getTeamColorValue", at = @At("HEAD"), cancellable = true)
    private void onGetTeamColorValue(CallbackInfoReturnable<Integer> cir) {
        Entity self = (Entity)(Object)this;

        // Check if this entity is a player and should glow
        if (self instanceof PlayerEntity && GlowManager.shouldGlow(self)) {
            // Get the color from config
            int color = RunicModConfig.getInstance().getGlowColor();

            // Set the return value
            cir.setReturnValue(color);
            cir.cancel(); // Make sure we cancel the original method
        }
    }
}
