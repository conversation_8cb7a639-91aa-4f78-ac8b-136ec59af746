package net.marko.runicmod.mixin;

import net.marko.runicmod.GlowManager;
import net.minecraft.entity.Entity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(Entity.class)
public class EntityRendererMixin {

    /**
     * Injects into the isGlowing method to make certain entities glow client-side
     */
    @Inject(method = "isGlowing", at = @At("RETURN"), cancellable = true)
    private void onIsGlowing(CallbackInfoReturnable<Boolean> cir) {
        // If our GlowManager says this entity should glow, return true
        if (GlowManager.shouldGlow((Entity)(Object)this)) {
            cir.setReturnValue(true);
        }
    }
}
