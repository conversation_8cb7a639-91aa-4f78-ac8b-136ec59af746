package com.complexrunes;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import com.complexrunes.config.RuneModConfig;
import com.complexrunes.chat.ChatListener;
import com.complexrunes.render.RuneTextRenderer;
import com.complexrunes.title.TitleManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ComplexRunesMod implements ClientModInitializer {
	public static final Logger LOGGER = LoggerFactory.getLogger("ComplexRunes");

	// Set to true to enable debug features like sending chat messages
	public static final boolean DEBUG_MODE = false;

	@Override
	public void onInitializeClient() {
		LOGGER.info("Initializing Complex Runes Mod...");

		// Load config
        RuneModConfig.getInstance();

		// Register the chat listener
		ChatListener.register();

		// Register HUD renderer for custom text
        HudRenderCallback.EVENT.register((drawContext, tickDelta) -> {
            RuneTextRenderer.renderCountdowns(drawContext);
        });

		// Register shutdown hook
		ClientLifecycleEvents.CLIENT_STOPPING.register(client -> {
			LOGGER.info("Shutting down Complex Runes Mod...");
			ChatListener.shutdown();
			TitleManager.shutdown();
		});

		LOGGER.info("Complex Runes Mod initialized!");
	}
}
