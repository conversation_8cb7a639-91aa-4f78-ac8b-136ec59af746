package net.marko.runicmod.mixin;

import net.marko.runicmod.GlowManager;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.entity.EntityRenderDispatcher;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to handle custom glow colors for entities
 */
@Mixin(EntityRenderDispatcher.class)
public class EntityRenderDispatcherMixin {

    /**
     * Injects into the render method to apply custom glow colors
     */
    @Inject(method = "render", at = @At("HEAD"))
    private void onRender(Entity entity, double x, double y, double z, float yaw, float tickDelta,
                         MatrixStack matrices, VertexConsumerProvider vertexConsumers, int light, CallbackInfo ci) {
        // Check if the entity should glow according to our GlowManager
        if (entity instanceof PlayerEntity && GlowManager.shouldGlow(entity)) {
            // Set the entity to glow
            entity.setGlowing(true);
        }
    }
}
