Write-Host "Quick Build for Complex Runes Mod..." -ForegroundColor Green

# Set JAVA_HOME and build in one go
$env:JAVA_HOME = "C:\Program Files\Java\jdk-21.0.7"

if (Test-Path "$env:JAVA_HOME\bin\java.exe") {
    Write-Host "Using Java at: $env:JAVA_HOME" -ForegroundColor Green
    Write-Host "Building mod..." -ForegroundColor Yellow
    
    .\gradlew clean build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ BUILD SUCCESS!" -ForegroundColor Green
        Write-Host "Your mod is ready at: build\libs\complex-runes-1.0.0.jar" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Build failed. Check errors above." -ForegroundColor Red
    }
} else {
    Write-Host "Java not found at: $env:JAVA_HOME" -ForegroundColor Red
    Write-Host "Please check your Java 21 installation path." -ForegroundColor Yellow
}
