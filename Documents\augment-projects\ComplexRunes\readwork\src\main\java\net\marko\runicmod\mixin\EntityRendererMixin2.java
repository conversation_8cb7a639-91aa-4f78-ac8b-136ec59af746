package net.marko.runicmod.mixin;

import net.marko.runicmod.GlowManager;
import net.minecraft.client.render.RenderLayer;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.entity.EntityRenderer;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(EntityRenderer.class)
public class EntityRendererMixin2<T extends Entity> {

    /**
     * Injects at the start of the render method to apply custom rendering for glowing entities
     */
    @Inject(method = "render", at = @At("HEAD"))
    private void onRenderStart(T entity, float yaw, float tickDelta, MatrixStack matrices,
                              VertexConsumerProvider vertexConsumers, int light, CallbackInfo ci) {
        // Check if this entity should glow
        if (GlowManager.shouldGlow(entity) && entity.isGlowing()) {
            // The actual glow effect is handled by the EntityRendererMixin
            // This is just a hook for any additional rendering we might want to do
        }
    }
}
