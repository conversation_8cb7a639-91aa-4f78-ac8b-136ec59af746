@echo off
echo Quick Build for Complex Runes Mod...
echo.

REM Try common Java 21 paths and build
set JAVA_HOME=C:\Program Files\Java\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" goto build

set JAVA_HOME=C:\Program Files\Java\jdk-21.0.7
if exist "%JAVA_HOME%\bin\java.exe" goto build

set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
if exist "%JAVA_HOME%\bin\java.exe" goto build

set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" goto build

set JAVA_HOME=C:\Program Files\Oracle\Java\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" goto build

set JAVA_HOME=C:\Program Files\Microsoft\jdk-21
if exist "%JAVA_HOME%\bin\java.exe" goto build

echo Could not find Java 21 installation automatically.
echo Please run one of these commands manually:
echo.
echo set JAVA_HOME=C:\Program Files\Java\jdk-21 ^& gradlew clean build
echo set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot ^& gradlew clean build
echo.
echo (Replace the path with your actual Java 21 installation directory)
pause
exit /b 1

:build
echo Using JAVA_HOME: %JAVA_HOME%
echo.
gradlew clean build

if %ERRORLEVEL% equ 0 (
    echo.
    echo ================================
    echo Build SUCCESS!
    echo ================================
    echo Your mod is ready at: build\libs\complex-runes-1.0.0.jar
) else (
    echo.
    echo Build failed. Check errors above.
)

pause
