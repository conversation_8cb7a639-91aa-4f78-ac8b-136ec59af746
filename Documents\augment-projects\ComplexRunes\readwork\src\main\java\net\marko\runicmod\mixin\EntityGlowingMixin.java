package net.marko.runicmod.mixin;

import net.marko.runicmod.GlowManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

/**
 * Mixin to handle the glowing state of entities
 */
@Mixin(Entity.class)
public abstract class EntityGlowingMixin {
    
    @Shadow
    private boolean glowing;
    
    @Shadow
    public abstract boolean isGlowing();
    
    /**
     * Injects into the isGlowing method to control the glowing state
     */
    @Inject(method = "isGlowing", at = @At("HEAD"), cancellable = true)
    private void onIsGlowing(CallbackInfoReturnable<Boolean> cir) {
        Entity self = (Entity)(Object)this;
        
        // Only handle player entities
        if (self instanceof PlayerEntity) {
            // Check if this player should glow according to our GlowManager
            boolean shouldGlow = GlowManager.shouldGlow(self);
            
            // Override the return value
            cir.setReturnValue(shouldGlow);
            cir.cancel();
        }
    }
}
