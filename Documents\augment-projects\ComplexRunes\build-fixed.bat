@echo off
echo Building Complex Runes Mod for Minecraft 1.21.1...
echo.

echo Checking Java version...
java -version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found. Please install Java 21+
    pause
    exit /b 1
)

echo.
echo Detecting Java 21 installation...

REM Try common Java 21 installation paths
set JAVA21_PATHS=C:\Program Files\Java\jdk-21;C:\Program Files\Java\jdk-21.0.7;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot;C:\Program Files\Eclipse Adoptium\jdk-21;C:\Program Files\Microsoft\jdk-21;C:\Program Files\Oracle\Java\jdk-21

for %%p in (%JAVA21_PATHS%) do (
    if exist "%%p\bin\java.exe" (
        echo Found Java 21 at: %%p
        set JAVA_HOME=%%p
        goto found_java21
    )
)

REM If not found in common paths, try to detect from java command
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
)

echo Java version detected: %JAVA_VERSION%

REM Try to find Java 21 installation directory
for /f "tokens=2*" %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\JavaSoft\JDK" /s /v JavaHome 2^>nul ^| findstr "21\."') do (
    if exist "%%j\bin\java.exe" (
        echo Found Java 21 at: %%j
        set JAVA_HOME=%%j
        goto found_java21
    )
)

echo WARNING: Could not automatically detect Java 21 installation.
echo Using system Java (which appears to be Java 21 based on version check)
echo Setting JAVA_HOME to a reasonable default...

REM Set a reasonable default based on the java command location
for /f "tokens=*" %%i in ('where java 2^>nul') do (
    set JAVA_PATH=%%i
    goto found_java_path
)

:found_java_path
if defined JAVA_PATH (
    for %%i in ("%JAVA_PATH%") do set JAVA_HOME=%%~dpi..
    echo Setting JAVA_HOME to: %JAVA_HOME%
) else (
    echo ERROR: Could not determine Java installation path
    echo Please manually set JAVA_HOME to your Java 21 installation directory
    pause
    exit /b 1
)

:found_java21
echo Using JAVA_HOME: %JAVA_HOME%
echo.

echo Cleaning previous build...
call gradlew clean

echo.
echo Building mod...
call gradlew build

if %ERRORLEVEL% equ 0 (
    echo.
    echo ================================
    echo Build completed successfully!
    echo ================================
    echo.
    echo Built files are in: build\libs\
    dir build\libs\*.jar
    echo.
    echo To test the mod, run: gradlew runClient
) else (
    echo.
    echo ================================
    echo Build failed!
    echo ================================
    echo Please check the error messages above.
    echo.
    echo If the issue persists, try manually setting JAVA_HOME:
    echo set JAVA_HOME=C:\Program Files\Java\jdk-21
    echo.
    echo Or find your Java 21 installation and set JAVA_HOME to that path.
)

pause
