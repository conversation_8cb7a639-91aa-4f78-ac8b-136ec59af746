# Complex Runes - Minecraft Fabric Mod

A Minecraft Fabric mod that monitors chat messages for rune notifications and displays them with customizable overlays and countdown timers.

## Features

- **Chat Monitoring**: Automatically detects rune-related messages in chat
- **Customizable Keywords**: Configure which words trigger notifications
- **Regular Expression Support**: Advanced pattern matching for complex detection rules
- **On-Screen Notifications**: Beautiful, customizable notification display
- **Countdown Timers**: Shows remaining time for each notification
- **Draggable Interface**: Move notifications by dragging them
- **Configuration GUI**: Easy-to-use settings interface via ModMenu

## Installation

1. Install [Fabric Loader](https://fabricmc.net/use/installer/) for Minecraft 1.21.1
2. Download and install [Fabric API](https://modrinth.com/mod/fabric-api)
3. Download and install [ModMenu](https://modrinth.com/mod/modmenu) (optional, for config GUI)
4. Download and install [Cloth Config](https://modrinth.com/mod/cloth-config) (required for config GUI)
5. Place the Complex Runes mod file in your `mods` folder

## Usage

### Basic Setup
1. Launch Minecraft with the mod installed
2. Open ModMenu and find "Complex Runes" to configure settings
3. Configure your rune keywords and display preferences
4. The mod will automatically monitor chat for matching messages

### Configuration Options

#### Chat Monitoring
- **Enable Rune Monitoring**: Toggle the entire system on/off
- **Rune Keywords**: List of words that trigger notifications (default: "rune", "Rune", "RUNE")
- **Use Regular Expressions**: Enable advanced pattern matching
- **Regex Pattern**: Custom regex pattern for message detection
- **Case Sensitive**: Whether keyword matching is case-sensitive

#### Display Settings
- **Show Notifications**: Toggle on-screen notifications
- **Show Countdown Timer**: Display remaining time for each notification
- **Notification Position**: X and Y coordinates for notification placement (-1 for X means centered)
- **Text Scale**: Size multiplier for notification text (0.1x to 5.0x)
- **Draggable Text**: Allow moving notifications by dragging
- **Text Shadow**: Add shadow effect to text

## Building the Mod

### Prerequisites
- Java 21 or higher
- Gradle (included via wrapper)

### Building
```bash
# Windows
build.bat

# Or manually
gradlew clean build
```

### Running in Development
```bash
gradlew runClient
```

## Configuration File

The mod stores its configuration in `config/complex-runes.json`. You can manually edit this file or use the in-game GUI.

Example configuration:
```json
{
  "enabled": true,
  "runeKeywords": ["rune", "Rune", "RUNE"],
  "useRegex": false,
  "customRegexPattern": ".*[Rr]une.*",
  "caseSensitive": false,
  "displayDurationSeconds": 30,
  "showCountdown": true,
  "titleX": -1,
  "titleY": 50,
  "titleScale": 1.0,
  "titleColor": 16777215,
  "draggable": false,
  "showTitle": true,
  "useShadow": true
}
```

## Supported Message Formats

The mod can detect various rune message formats:
- "Player has activated a rune for X seconds"
- "Rune effect on Player will last X seconds"
- "Player's rune has expired"
- Generic messages containing rune keywords with duration

## Troubleshooting

### Common Issues
1. **Notifications not appearing**: Check that the mod is enabled in configuration
2. **Keywords not matching**: Verify case sensitivity settings
3. **Text not visible**: Adjust position settings and text scale
4. **Performance issues**: Disable draggable text or reduce text scale

### Debug Mode
The mod includes debug logging. Check the Minecraft logs for detailed information about detected messages.

## License

This project is licensed under the MIT License.

## Credits

- Built with [Fabric](https://fabricmc.net/)
- Configuration GUI powered by [Cloth Config](https://github.com/shedaniel/ClothConfig)
- ModMenu integration for easy access
- Based on reference implementation patterns
