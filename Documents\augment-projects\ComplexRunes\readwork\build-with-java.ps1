$javaExe = "C:\Program Files\Java\jdk-21\bin\java.exe"
Write-Host "Using Java executable: $javaExe"

# Create a temporary batch file that uses the specific Java executable
$tempBatchContent = @"
@echo off
set JAVA_EXE=$javaExe
echo Using Java executable: %JAVA_EXE%
"%JAVA_EXE%" -Dorg.gradle.java.home="C:\Program Files\Java\jdk-21" -jar gradle\wrapper\gradle-wrapper.jar --no-daemon build
"@

$tempBatchFile = "temp-build.bat"
$tempBatchContent | Out-File -FilePath $tempBatchFile -Encoding ASCII

# Run the temporary batch file
Write-Host "Running temporary build script..."
& .\$tempBatchFile

# Clean up
Remove-Item $tempBatchFile
