{"schemaVersion": 1, "id": "complex-runes", "version": "${version}", "name": "Complex Runes", "description": "A Minecraft mod that monitors chat for rune notifications and displays them with customizable overlays and countdown timers.", "authors": ["ComplexRunes Team"], "contact": {"homepage": "https://github.com/complexrunes/complex-runes", "sources": "https://github.com/complexrunes/complex-runes"}, "license": "MIT", "icon": "assets/complex-runes/icon.png", "environment": "client", "entrypoints": {"client": ["com.complexrunes.ComplexRunesMod"], "modmenu": ["com.complexrunes.config.ModMenuIntegration"]}, "mixins": ["complex-runes.mixins.json"], "depends": {"fabricloader": ">=${loader_version}", "fabric-api": "*", "minecraft": "~1.21.1", "java": ">=21"}, "suggests": {"modmenu": "*"}}